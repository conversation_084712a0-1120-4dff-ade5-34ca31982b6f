from extensions import db
from datetime import datetime

class User(db.Model):
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.<PERSON>umn(db.String(100), nullable=False)
    phone = db.Column(db.String(20), unique=True, nullable=False)
    is_admin = db.Column(db.<PERSON>, default=False)
    profile_photo = db.Column(db.String(255), nullable=True)  # Путь к фото профиля
    created_tasks = db.relationship('Task', foreign_keys='Task.creator_id', backref='creator', lazy=True)
    assigned_tasks = db.relationship('Task', foreign_keys='Task.assignee_id', backref='assignee', lazy=True)

class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    creator_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('user.id'), nullable=False)
    assignee_id = db.Column(db.Integer, db.<PERSON><PERSON>('user.id'), nullable=False)
    deadline = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(50), nullable=False, default='ждет принятия задачи')
    task_type = db.Column(db.String(20), nullable=False)  # 'общая' или 'личная'
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)