{% extends "base.html" %}
{% from "task_list.html" import render_task_list %}

{% block title %}Архив задач - Dunix WorkFlow{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-2">📁 Архив выполненных задач</h2>
                <p class="text-secondary mb-0">
                    {% if is_admin %}
                        Все выполненные задачи в системе
                    {% else %}
                        Ваши выполненные задачи
                    {% endif %}
                </p>
            </div>
            <div class="archive-stats">
                <span class="badge bg-success fs-6 px-3 py-2">
                    📊 Всего: {{ tasks.total }} задач
                </span>
            </div>
        </div>

        {% if tasks.items %}
            <!-- Архивные задачи используют тот же макрос, но с отключенными действиями -->
            {% for task in tasks.items %}
                <div class="card mb-3 archive-task-card"
                     data-task-id="{{ task.id }}"
                     data-task-status="{{ task.status }}"
                     data-task-assignee="{{ task.assignee_id }}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-1">{{ task.title }}</h5>
                            <small class="text-muted">
                                Выполнена: {{ task.deadline.strftime('%d.%m.%Y %H:%M') }}
                            </small>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-success task-status-badge">
                                ✅ Выполнена
                            </span>
                            {% if task.task_type == 'личная' %}
                                <span class="badge bg-secondary">
                                    👤 Личная
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    👥 Общая
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="card-text mb-3">{{ task.description }}</p>

                        <div class="row">
                            <div class="col-md-6">
                                {% if is_admin or task.assignee_id != current_user_id %}
                                    <p class="mb-1"><strong>Исполнитель:</strong> {{ task.assignee.name }}</p>
                                {% endif %}
                                <p class="mb-1"><strong>Создал:</strong> {{ task.creator.name }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Срок был:</strong> {{ task.deadline.strftime('%d.%m.%Y %H:%M') }}</p>
                                <p class="mb-1"><strong>Создано:</strong> {{ task.created_at.strftime('%d.%m.%Y %H:%M') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-success bg-opacity-10">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-success fw-bold">
                                🎉 Задача успешно выполнена
                            </span>
                            {% if is_admin %}
                                <small class="text-muted">
                                    Архивная запись
                                </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}

            <!-- Пагинация -->
            {% if tasks.pages > 1 %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if tasks.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('archive_tasks', page=tasks.prev_num) }}">
                                    ← Предыдущая
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page in range(1, tasks.pages + 1) %}
                            {% if page <= 3 or page > tasks.pages - 3 or (page >= tasks.page - 1 and page <= tasks.page + 1) %}
                                <li class="page-item {% if page == tasks.page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('archive_tasks', page=page) }}">{{ page }}</a>
                                </li>
                            {% elif page == 4 and tasks.page > 5 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% elif page == tasks.pages - 3 and tasks.page < tasks.pages - 4 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if tasks.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('archive_tasks', page=tasks.next_num) }}">
                                    Следующая →
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="display-1">📂</i>
                </div>
                <h4 class="text-muted mb-3">Архив пуст</h4>
                <p class="text-secondary">
                    {% if is_admin %}
                        Пока нет выполненных задач в системе
                    {% else %}
                        У вас пока нет выполненных задач
                    {% endif %}
                </p>
                <div class="mt-4">
                    {% if is_admin %}
                        <a href="{{ url_for('all_tasks') }}" class="btn btn-primary">
                            Посмотреть активные задачи
                        </a>
                    {% else %}
                        <a href="{{ url_for('my_tasks') }}" class="btn btn-primary">
                            Мои активные задачи
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
