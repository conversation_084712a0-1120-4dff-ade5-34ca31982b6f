{% extends "base.html" %}

{% block title %}Архив задач - Dunix WorkFlow{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Заголовок и фильтры -->
        <div class="archive-header">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="archive-title">Архив задач</h2>
                    <p class="archive-subtitle">
                        {% if is_admin %}
                            Все выполненные задачи в системе
                        {% else %}
                            Ваши выполненные задачи
                        {% endif %}
                    </p>
                </div>
                <div class="archive-stats">
                    <span class="stats-badge">
                        Всего: {{ tasks.total }}
                    </span>
                </div>
            </div>

            <!-- Фильтры по дате -->
            <div class="archive-filters mb-4">
                <form method="GET" class="filter-form">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">С даты</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                   value="{{ request.args.get('date_from', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">По дату</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                   value="{{ request.args.get('date_to', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="sort_by" class="form-label">Сортировка</label>
                            <select class="form-select" id="sort_by" name="sort_by">
                                <option value="newest" {% if request.args.get('sort_by') == 'newest' %}selected{% endif %}>Сначала новые</option>
                                <option value="oldest" {% if request.args.get('sort_by') == 'oldest' %}selected{% endif %}>Сначала старые</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">Применить</button>
                                <a href="{{ url_for('archive_tasks') }}" class="btn btn-outline-secondary">Сбросить</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if tasks.items %}
            <!-- Минималистичные карточки архивных задач -->
            <div class="archive-tasks-grid">
                {% for task in tasks.items %}
                    <div class="archive-task-card" data-task-id="{{ task.id }}">
                        <!-- Заголовок карточки -->
                        <div class="task-header">
                            <div class="task-title-section">
                                <h5 class="task-title">{{ task.title }}</h5>
                            </div>
                            <div class="task-status">
                                <span class="status-badge completed">Выполнена</span>
                                <span class="task-type-badge {% if task.task_type == 'личная' %}personal{% else %}general{% endif %}">
                                    {{ 'Личная' if task.task_type == 'личная' else 'Общая' }}
                                </span>
                            </div>
                        </div>

                        <!-- Описание -->
                        <div class="task-description">
                            <p>{{ task.description }}</p>
                        </div>

                        <!-- Информация о задаче -->
                        <div class="task-info">
                            <div class="info-grid">
                                {% if is_admin or task.assignee_id != current_user_id %}
                                    <div class="info-item">
                                        <span class="info-label">Исполнитель</span>
                                        <span class="info-value">{{ task.assignee.name }}</span>
                                    </div>
                                {% endif %}
                                <div class="info-item">
                                    <span class="info-label">Создал</span>
                                    <span class="info-value">{{ task.creator.name }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Срок был</span>
                                    <span class="info-value">{{ task.deadline.strftime('%d.%m.%Y %H:%M') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Пагинация -->
            {% if tasks.pages > 1 %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if tasks.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('archive_tasks', page=tasks.prev_num) }}">
                                    ← Предыдущая
                                </a>
                            </li>
                        {% endif %}

                        {% for page in range(1, tasks.pages + 1) %}
                            {% if page <= 3 or page > tasks.pages - 3 or (page >= tasks.page - 1 and page <= tasks.page + 1) %}
                                <li class="page-item {% if page == tasks.page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('archive_tasks', page=page) }}">{{ page }}</a>
                                </li>
                            {% elif page == 4 and tasks.page > 5 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% elif page == tasks.pages - 3 and tasks.page < tasks.pages - 4 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if tasks.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('archive_tasks', page=tasks.next_num) }}">
                                    Следующая →
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="empty-archive">
                <div class="empty-content">
                    <h4 class="empty-title">Архив пуст</h4>
                    <p class="empty-subtitle">
                        {% if is_admin %}
                            Пока нет выполненных задач в системе
                        {% else %}
                            У вас пока нет выполненных задач
                        {% endif %}
                    </p>
                    <div class="empty-actions">
                        {% if is_admin %}
                            <a href="{{ url_for('all_tasks') }}" class="btn btn-primary">
                                Посмотреть активные задачи
                            </a>
                        {% else %}
                            <a href="{{ url_for('my_tasks') }}" class="btn btn-primary">
                                Мои активные задачи
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
// Исправляем отображение дат в фильтрах для формата dd/mm/yyyy
document.addEventListener('DOMContentLoaded', function() {
    const dateInputs = document.querySelectorAll('input[type="date"]');

    dateInputs.forEach(function(input) {
        // Устанавливаем атрибуты для правильного отображения
        input.setAttribute('lang', 'ru-RU');

        // Если есть значение, конвертируем его в правильный формат для отображения
        if (input.value) {
            const date = new Date(input.value);
            if (!isNaN(date.getTime())) {
                // Форматируем дату как dd.mm.yyyy для отображения пользователю
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = date.getFullYear();

                // Создаем текстовое поле для отображения
                const displayInput = document.createElement('input');
                displayInput.type = 'text';
                displayInput.value = `${day}.${month}.${year}`;
                displayInput.placeholder = 'дд.мм.гггг';
                displayInput.className = input.className;
                displayInput.name = input.name + '_display';
                displayInput.readOnly = true;

                // Скрываем оригинальное поле
                input.style.display = 'none';

                // Вставляем отображаемое поле
                input.parentNode.insertBefore(displayInput, input);

                // При клике на отображаемое поле показываем календарь
                displayInput.addEventListener('click', function() {
                    input.style.display = 'block';
                    displayInput.style.display = 'none';
                    input.focus();
                });

                // При изменении даты обновляем отображение
                input.addEventListener('change', function() {
                    if (this.value) {
                        const newDate = new Date(this.value);
                        const newDay = String(newDate.getDate()).padStart(2, '0');
                        const newMonth = String(newDate.getMonth() + 1).padStart(2, '0');
                        const newYear = newDate.getFullYear();
                        displayInput.value = `${newDay}.${newMonth}.${newYear}`;
                    } else {
                        displayInput.value = '';
                    }
                    this.style.display = 'none';
                    displayInput.style.display = 'block';
                });

                // При потере фокуса возвращаемся к отображению
                input.addEventListener('blur', function() {
                    setTimeout(() => {
                        this.style.display = 'none';
                        displayInput.style.display = 'block';
                    }, 100);
                });
            }
        } else {
            // Для пустых полей создаем текстовое поле с placeholder
            const displayInput = document.createElement('input');
            displayInput.type = 'text';
            displayInput.placeholder = 'дд.мм.гггг';
            displayInput.className = input.className;
            displayInput.name = input.name + '_display';
            displayInput.readOnly = true;

            input.style.display = 'none';
            input.parentNode.insertBefore(displayInput, input);

            displayInput.addEventListener('click', function() {
                input.style.display = 'block';
                displayInput.style.display = 'none';
                input.focus();
            });

            input.addEventListener('change', function() {
                if (this.value) {
                    const date = new Date(this.value);
                    const day = String(date.getDate()).padStart(2, '0');
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const year = date.getFullYear();
                    displayInput.value = `${day}.${month}.${year}`;
                } else {
                    displayInput.value = '';
                }
                this.style.display = 'none';
                displayInput.style.display = 'block';
            });

            input.addEventListener('blur', function() {
                setTimeout(() => {
                    this.style.display = 'none';
                    displayInput.style.display = 'block';
                }, 100);
            });
        }
    });
});
</script>

{% endblock %}
