{% macro render_task_list(tasks, show_assignee=False, now=None, is_admin=False, current_user_id=None) %}
    {% for task in tasks.items %}
        <div class="card mb-3 {% if now and task.deadline < now %}border-danger{% endif %}"
             data-task-id="{{ task.id }}"
             data-task-status="{{ task.status }}"
             data-task-assignee="{{ task.assignee_id }}"
             data-task-overdue="{% if now and task.deadline < now %}true{% else %}false{% endif %}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-1">{{ task.title }}</h5>
                    <small class="text-muted">
                        {% if now and task.deadline < now %}
                            Просрочена
                        {% else %}
                            {{ task.deadline.strftime('%d.%m.%Y %H:%M') }}
                        {% endif %}
                    </small>
                </div>
                <div class="d-flex align-items-center gap-2">
                    {% if task.status == 'ждет принятия задачи' %}
                        <span class="badge bg-warning task-status-badge" data-status="{{ task.status }}">
                            {{ task.status }}
                        </span>
                    {% elif task.status == 'в процессе' %}
                        <span class="badge bg-primary task-status-badge" data-status="{{ task.status }}">
                            {{ task.status }}
                        </span>
                    {% elif task.status == 'проверяется админом' %}
                        <span class="badge bg-info task-status-badge" data-status="{{ task.status }}">
                            {{ task.status }}
                        </span>
                    {% else %}
                        <span class="badge bg-success task-status-badge" data-status="{{ task.status }}">
                            {{ task.status }}
                        </span>
                    {% endif %}

                    {% if task.task_type == 'личная' %}
                        <span class="badge bg-secondary">
                            Личная
                        </span>
                    {% else %}
                        <span class="badge bg-secondary">
                            Общая
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <p class="card-text mb-3">{{ task.description }}</p>

                <div class="row">
                    <div class="col-md-6">
                        {% if show_assignee %}
                            <p class="mb-1"><strong>Исполнитель:</strong> {{ task.assignee.name }}</p>
                        {% endif %}
                        <p class="mb-1"><strong>Создал:</strong> {{ task.creator.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Срок:</strong> {{ task.deadline.strftime('%d.%m.%Y %H:%M') }}</p>
                        <p class="mb-1"><strong>Создано:</strong> {{ task.created_at.strftime('%d.%m.%Y %H:%M') }}</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex flex-wrap gap-2 task-actions" data-task-id="{{ task.id }}">
                    {% if task.status == 'ждет принятия задачи' and task.assignee_id == current_user_id %}
                        <form method="POST" action="{{ url_for('update_task_status_route', task_id=task.id) }}" class="d-inline">
                            <input type="hidden" name="status" value="в процессе">
                            <button type="submit" class="btn btn-success btn-sm">
                                Принять задачу
                            </button>
                        </form>
                    {% endif %}

                    {% if task.status == 'в процессе' and task.assignee_id == current_user_id %}
                        <form method="POST" action="{{ url_for('update_task_status_route', task_id=task.id) }}" class="d-inline">
                            <input type="hidden" name="status" value="проверяется админом">
                            <button type="submit" class="btn btn-primary btn-sm">
                                Отправить на проверку
                            </button>
                        </form>
                    {% endif %}

                    {% if task.status == 'проверяется админом' and is_admin %}
                        <form method="POST" action="{{ url_for('approve_task', task_id=task.id) }}" class="d-inline">
                            <button type="submit" class="btn btn-success btn-sm">
                                Принять
                            </button>
                        </form>
                        <form method="POST" action="{{ url_for('reject_task', task_id=task.id) }}" class="d-inline">
                            <button type="submit" class="btn btn-warning btn-sm">
                                Отклонить
                            </button>
                        </form>
                    {% endif %}

                    {% if is_admin %}
                        <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn btn-warning btn-sm">
                            Редактировать
                        </a>
                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ task.id }}">
                            Удалить
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>

        {% if is_admin %}
            <!-- Модальное окно подтверждения удаления -->
            <div class="modal fade" id="deleteModal{{ task.id }}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Подтверждение удаления</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Вы уверены, что хотите удалить задачу "{{ task.title }}"?</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <form method="POST" action="{{ url_for('delete_task_route', task_id=task.id) }}" class="d-inline">
                                <button type="submit" class="btn btn-danger">Удалить</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    {% else %}
        <div class="alert alert-info">Задачи не найдены</div>
    {% endfor %}

    {% if tasks.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% for page in range(1, tasks.pages + 1) %}
                    <li class="page-item {% if page == tasks.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for(request.endpoint, page=page) }}">{{ page }}</a>
                    </li>
                {% endfor %}
            </ul>
        </nav>
    {% endif %}
{% endmacro %}