{% extends "base.html" %}

{% block title %}Главная - Dunix WorkFlow{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5">
                <h1 class="mb-3">Панель управления задачами</h1>
                <p class="text-secondary">Простое и эффективное управление задачами</p>
            </div>

            {% if user.is_admin %}
                <!-- Админская панель с таблицей сотрудников -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Сотрудники</h3>
                            <div>
                                <a href="{{ url_for('create_task_route') }}" class="btn btn-primary me-2">
                                    Создать задачу
                                </a>
                                <a href="{{ url_for('all_tasks') }}" class="btn btn-secondary">
                                    Все задачи
                                </a>
                            </div>
                        </div>

                        <!-- Минималистичные карточки сотрудников -->
                        <div class="employees-grid">
                            {% for employee in employees %}
                            <div class="employee-card {% if employee.is_admin %}admin-card{% endif %}" data-employee-id="{{ employee.id }}">
                                <!-- Шестеренка для настроек фото -->
                                <div class="photo-settings">
                                    <button class="settings-btn" onclick="openPhotoModal({{ employee.id }}, '{{ employee.name }}', {% if employee.profile_photo %}'{{ url_for('uploaded_file', filename=employee.profile_photo) }}'{% else %}null{% endif %})">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                </div>

                                <!-- Аватар -->
                                <div class="employee-avatar">
                                    <div class="avatar-circle {% if employee.is_admin %}admin-avatar{% else %}employee-avatar{% endif %}">
                                        {% if employee.profile_photo %}
                                            <img src="{{ url_for('uploaded_file', filename=employee.profile_photo) }}" alt="{{ employee.name }}" class="profile-photo">
                                        {% else %}
                                            <span class="avatar-initials">{{ employee.name.split()[0][0] }}{% if employee.name.split()|length > 1 %}{{ employee.name.split()[1][0] }}{% endif %}</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Информация о сотруднике -->
                                <div class="employee-info">
                                    <h3 class="employee-name">{{ employee.name }}</h3>
                                    <p class="employee-phone">{{ format_phone_number(employee.phone) }}</p>

                                    <!-- Роль -->
                                    <div class="employee-role">
                                        {% if employee.is_admin %}
                                            <span class="role-badge admin-role">
                                                Администратор
                                            </span>
                                        {% else %}
                                            <span class="role-badge employee-role">
                                                Сотрудник
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Статистика задач -->
                                {% if not employee.is_admin %}
                                <div class="employee-stats">
                                    <div class="stat-item">
                                        <div class="stat-number active-tasks-count">{{ employee.active_tasks_count }}</div>
                                        <div class="stat-label">Активные</div>
                                    </div>

                                    <div class="stat-item">
                                        <div class="stat-number completed-tasks-count">{{ employee.completed_tasks_count }}</div>
                                        <div class="stat-label">Выполнено</div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="admin-stats">
                                    <div class="admin-badge">
                                        Управление системой
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Действия -->
                                <div class="employee-actions">
                                    {% if not employee.is_admin %}
                                        <a href="{{ url_for('create_task_route') }}?assignee={{ employee.id }}"
                                           class="action-btn">
                                            Назначить задачу
                                        </a>
                                    {% else %}
                                        <div class="admin-actions">
                                            <span class="admin-status">
                                                Системный администратор
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>


                    </div>
                </div>
            {% else %}
                <!-- Обычная панель для сотрудников -->
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Личные задачи</h5>
                                <p class="card-text flex-grow-1">Управляйте своими личными задачами</p>
                                <a href="{{ url_for('create_personal_task_route') }}" class="btn btn-success">
                                    Создать личную задачу
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Мои задачи</h5>
                                <p class="card-text flex-grow-1">Просмотр назначенных вам задач</p>
                                <a href="{{ url_for('my_tasks') }}" class="btn btn-primary">
                                    Мои задачи
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Общие задачи</h5>
                                <p class="card-text flex-grow-1">Просматривайте общедоступные задачи</p>
                                <a href="{{ url_for('public_tasks') }}" class="btn btn-secondary">
                                    Общие задачи
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Модальное окно для управления фотографией -->
<div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">Управление фотографией</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div class="current-photo-preview">
                        <div class="preview-circle">
                            <img id="currentPhotoPreview" src="" alt="" class="current-photo" style="display: none;">
                            <span id="currentInitials" class="preview-initials"></span>
                        </div>
                    </div>
                    <p class="mt-2"><strong id="employeeName"></strong></p>
                </div>

                <!-- Форма загрузки фото -->
                <form id="uploadPhotoForm" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="photoFile" class="form-label">Выберите фотографию</label>
                        <input type="file" class="form-control" id="photoFile" name="photo" accept=".jpg,.jpeg,.png">
                        <div class="form-text">Разрешены форматы: JPG, JPEG, PNG. Максимальный размер: 16MB</div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Загрузить фотографию
                        </button>
                    </div>
                </form>

                <!-- Кнопка удаления фото -->
                <div class="mt-3" id="deletePhotoSection" style="display: none;">
                    <hr>
                    <form id="deletePhotoForm" method="POST">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="fas fa-trash"></i> Удалить фотографию
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}