{% extends "base.html" %}

{% block title %}Главная - Dunix WorkFlow{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5">
                <h1 class="mb-3">Панель управления задачами</h1>
                <p class="text-secondary">Простое и эффективное управление задачами</p>
            </div>

            {% if user.is_admin %}
                <!-- Админская панель с таблицей сотрудников -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Сотрудники</h3>
                            <div>
                                <a href="{{ url_for('create_task_route') }}" class="btn btn-primary me-2">
                                    Создать задачу
                                </a>
                                <a href="{{ url_for('all_tasks') }}" class="btn btn-secondary">
                                    Все задачи
                                </a>
                            </div>
                        </div>

                        <!-- Красивые карточки сотрудников -->
                        <div class="employees-grid">
                            {% for employee in employees %}
                            <div class="employee-card {% if employee.is_admin %}admin-card{% endif %}" data-employee-id="{{ employee.id }}">
                                <!-- Аватар с градиентом -->
                                <div class="employee-avatar">
                                    <div class="avatar-circle {% if employee.is_admin %}admin-avatar{% else %}employee-avatar{% endif %}">
                                        <span class="avatar-initials">{{ employee.name.split()[0][0] }}{% if employee.name.split()|length > 1 %}{{ employee.name.split()[1][0] }}{% endif %}</span>
                                    </div>
                                    {% if employee.is_admin %}
                                        <div class="admin-crown">👑</div>
                                    {% endif %}
                                </div>

                                <!-- Информация о сотруднике -->
                                <div class="employee-info">
                                    <h3 class="employee-name">{{ employee.name }}</h3>
                                    <p class="employee-phone">{{ employee.phone }}</p>

                                    <!-- Роль с красивым бейджем -->
                                    <div class="employee-role">
                                        {% if employee.is_admin %}
                                            <span class="role-badge admin-role">
                                                <i class="role-icon">⚡</i>
                                                Администратор
                                            </span>
                                        {% else %}
                                            <span class="role-badge employee-role">
                                                <i class="role-icon">👤</i>
                                                Сотрудник
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Статистика задач -->
                                {% if not employee.is_admin %}
                                <div class="employee-stats">
                                    <div class="stat-item active-stat">
                                        <div class="stat-circle">
                                            <div class="stat-number active-tasks-count">{{ employee.active_tasks_count }}</div>
                                        </div>
                                        <div class="stat-label">Активные</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar active-progress" style="width: {{ (employee.active_tasks_count / (employee.active_tasks_count + employee.completed_tasks_count + 1) * 100) }}%"></div>
                                        </div>
                                    </div>

                                    <div class="stat-item completed-stat">
                                        <div class="stat-circle">
                                            <div class="stat-number completed-tasks-count">{{ employee.completed_tasks_count }}</div>
                                        </div>
                                        <div class="stat-label">Выполнено</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar completed-progress" style="width: {{ (employee.completed_tasks_count / (employee.active_tasks_count + employee.completed_tasks_count + 1) * 100) }}%"></div>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="admin-stats">
                                    <div class="admin-badge">
                                        <i class="admin-icon">🛡️</i>
                                        <span>Управление системой</span>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Действия -->
                                <div class="employee-actions">
                                    {% if not employee.is_admin %}
                                        <a href="{{ url_for('create_task_route') }}?assignee={{ employee.id }}"
                                           class="action-btn assign-task-btn">
                                            <i class="btn-icon">📋</i>
                                            <span>Назначить задачу</span>
                                        </a>
                                    {% else %}
                                        <div class="admin-actions">
                                            <span class="admin-status">
                                                <i class="status-icon">⚙️</i>
                                                Системный администратор
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>


                    </div>
                </div>
            {% else %}
                <!-- Обычная панель для сотрудников -->
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Личные задачи</h5>
                                <p class="card-text flex-grow-1">Управляйте своими личными задачами</p>
                                <a href="{{ url_for('create_personal_task_route') }}" class="btn btn-success">
                                    Создать личную задачу
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Мои задачи</h5>
                                <p class="card-text flex-grow-1">Просмотр назначенных вам задач</p>
                                <a href="{{ url_for('my_tasks') }}" class="btn btn-primary">
                                    Мои задачи
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column text-center">
                                <h5 class="card-title">Общие задачи</h5>
                                <p class="card-text flex-grow-1">Просматривайте общедоступные задачи</p>
                                <a href="{{ url_for('public_tasks') }}" class="btn btn-secondary">
                                    Общие задачи
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}