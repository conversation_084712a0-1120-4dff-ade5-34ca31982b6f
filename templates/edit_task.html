{% extends "base.html" %}

{% block title %}Редактирование задачи{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Редактирование задачи</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="title" class="form-label">Название задачи</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="{{ task.title }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Описание задачи</label>
                        <textarea class="form-control" id="description" name="description" 
                                rows="3" required>{{ task.description }}</textarea>
                    </div>
                    <div class="mb-3">
                        <label for="assignee" class="form-label">Исполнитель</label>
                        <select class="form-select" id="assignee" name="assignee_id" required>
                            {% for employee in employees %}
                                <option value="{{ employee.id }}" 
                                        {% if employee.id == task.assignee_id %}selected{% endif %}>
                                    {{ employee.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="deadline" class="form-label">Срок выполнения</label>
                        <input type="datetime-local" class="form-control" id="deadline" 
                               name="deadline" value="{{ task.deadline.strftime('%Y-%m-%dT%H:%M') }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="task_type" class="form-label">Тип задачи</label>
                        <select class="form-select" id="task_type" name="task_type" required>
                            <option value="личная" {% if task.task_type == 'личная' %}selected{% endif %}>
                                Личная
                            </option>
                            <option value="общая" {% if task.task_type == 'общая' %}selected{% endif %}>
                                Общая
                            </option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Сохранить изменения</button>
                        <a href="{{ url_for('all_tasks') }}" class="btn btn-secondary">Отмена</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 