from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, send_from_directory
from datetime import datetime
import os
from werkzeug.utils import secure_filename
from PIL import Image
from extensions import db
from models import User, Task
from database import (create_user, get_user_by_phone, create_task, get_user_tasks,
                     get_all_tasks, get_archived_tasks, get_user_archived_tasks,
                     update_task_status, delete_task)

app = Flask(__name__)
app.config['SECRET_KEY'] = os.urandom(24)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///tasks.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Конфигурация для загрузки файлов
app.config['UPLOAD_FOLDER'] = 'static/uploads/profile_photos'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def normalize_phone_number(phone):
    """Нормализует номер телефона для хранения в базе данных"""
    if not phone:
        return phone

    # Убираем все нецифровые символы
    digits = ''.join(filter(str.isdigit, phone))

    # Если номер 9 цифр, добавляем 998 в начало
    if len(digits) == 9:
        return f"998{digits}"
    # Если номер уже содержит 998 в начале
    elif len(digits) == 12 and digits.startswith('998'):
        return digits
    else:
        # Возвращаем как есть
        return phone

def format_phone_number(phone):
    """Форматирует номер телефона в красивый вид +998 (99) 895 32-00"""
    if not phone:
        return phone

    # Убираем все нецифровые символы
    digits = ''.join(filter(str.isdigit, phone))

    # Если номер 12 цифр и начинается с 998
    if len(digits) == 12 and digits.startswith('998'):
        # Формат: 998998953200 -> +998 (99) 895 32-00
        return f"+998 ({digits[3:5]}) {digits[5:8]} {digits[8:10]}-{digits[10:12]}"
    else:
        # Возвращаем как есть, если формат не распознан
        return phone

db.init_app(app)

# Добавляем функцию форматирования в контекст шаблонов
@app.context_processor
def utility_processor():
    return dict(format_phone_number=format_phone_number)

def init_db():
    with app.app_context():
        db.create_all()
        # Проверяем, есть ли уже пользователи в базе
        if not User.query.first():
            print("База данных создана успешно!")

# Добавляем пользователя во все шаблоны
@app.context_processor
def inject_user():
    if 'user_id' in session:
        user = User.query.get(session['user_id'])
        return {'user': user}
    return {'user': None}

def login_required(f):
    def wrapper(*args, **kwargs):
        if 'user_id' not in session:
            flash('Пожалуйста, войдите в систему', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

def admin_required(f):
    def wrapper(*args, **kwargs):
        if 'user_id' not in session:
            flash('Пожалуйста, войдите в систему', 'warning')
            return redirect(url_for('login'))
        user = User.query.get(session['user_id'])
        if not user or not user.is_admin:
            flash('Доступ запрещен', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

# Базовый маршрут
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    user = User.query.get(session['user_id'])
    if not user:
        session.clear()
        return redirect(url_for('login'))

    # Если пользователь админ, получаем данные о сотрудниках
    employees = []
    if user.is_admin:
        # Получаем всех пользователей, сначала админы, потом сотрудники
        all_users = User.query.order_by(User.is_admin.desc(), User.name).all()
        for employee in all_users:
            if employee.is_admin:
                # Для админов не считаем задачи
                employee.active_tasks_count = 0
                employee.completed_tasks_count = 0
            else:
                # Подсчитываем активные задачи (не выполненные) только для сотрудников
                active_tasks = Task.query.filter(
                    Task.assignee_id == employee.id,
                    Task.status.in_(['ждет принятия задачи', 'в процессе', 'проверяется админом'])
                ).count()

                # Подсчитываем выполненные задачи только для сотрудников
                completed_tasks = Task.query.filter(
                    Task.assignee_id == employee.id,
                    Task.status == 'выполнена'
                ).count()

                employee.active_tasks_count = active_tasks
                employee.completed_tasks_count = completed_tasks

            employees.append(employee)

    return render_template('index.html', user=user, employees=employees)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        phone = request.form.get('phone')
        # Нормализуем номер телефона для поиска
        normalized_phone = normalize_phone_number(phone)
        user = get_user_by_phone(normalized_phone)

        if user:
            session['user_id'] = user.id
            flash('Вы успешно вошли в систему!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Пользователь с таким номером телефона не найден', 'danger')

    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')

        # Нормализуем номер телефона
        normalized_phone = normalize_phone_number(phone)

        if get_user_by_phone(normalized_phone):
            flash('Пользователь с таким номером телефона уже существует', 'danger')
            return redirect(url_for('register'))

        try:
            user = create_user(name, normalized_phone)
            session['user_id'] = user.id
            flash('Регистрация успешна!', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            flash('Ошибка при регистрации. Попробуйте еще раз.', 'danger')
            print(f"Error: {e}")

    return render_template('register.html')

@app.route('/logout', methods=['POST'])
def logout():
    session.clear()
    flash('Вы вышли из системы', 'info')
    return redirect(url_for('login'))

# Маршруты для задач
@app.route('/tasks/create', methods=['GET', 'POST'])
@admin_required
def create_task_route():
    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        assignee_id = request.form.get('assignee_id')
        deadline_str = request.form.get('deadline')
        task_type = request.form.get('task_type')

        try:
            # Преобразуем строку даты в объект datetime
            deadline = datetime.strptime(deadline_str, '%Y-%m-%d')
            # Устанавливаем время на конец дня (23:59:59)
            deadline = deadline.replace(hour=23, minute=59, second=59)

            task = create_task(
                title,
                description,
                session['user_id'],
                assignee_id,
                deadline,
                task_type
            )
            # Проверяем, назначена ли задача самому себе или другому
            if session['user_id'] == assignee_id:
                flash('Задача успешно создана и взята в работу!', 'success')
            else:
                flash('Задача успешно создана и назначена сотруднику!', 'success')
            return redirect(url_for('all_tasks'))
        except Exception as e:
            flash('Ошибка при создании задачи', 'danger')
            print(f"Error: {e}")

    employees = User.query.filter_by(is_admin=False).all()
    return render_template('create_task.html', employees=employees)

@app.route('/tasks/personal/create', methods=['GET', 'POST'])
@login_required
def create_personal_task_route():
    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        deadline_str = request.form.get('deadline')

        try:
            # Преобразуем строку даты в объект datetime
            deadline = datetime.strptime(deadline_str, '%Y-%m-%d')
            # Устанавливаем время на конец дня (23:59:59)
            deadline = deadline.replace(hour=23, minute=59, second=59)

            task = create_task(
                title,
                description,
                session['user_id'],
                session['user_id'],
                deadline,
                'личная'
            )
            flash('Личная задача успешно создана и взята в работу!', 'success')
            return redirect(url_for('my_tasks'))
        except Exception as e:
            flash('Ошибка при создании задачи', 'danger')
            print(f"Error: {e}")

    return render_template('create_personal_task.html')

@app.route('/tasks/my')
@login_required
def my_tasks():
    page = request.args.get('page', 1, type=int)
    tasks = get_user_tasks(session['user_id'], page)
    user = User.query.get(session['user_id'])
    return render_template('my_tasks.html', tasks=tasks, now=datetime.utcnow(),
                         current_user_id=session['user_id'], is_admin=user.is_admin)

@app.route('/tasks/all')
@admin_required
def all_tasks():
    page = request.args.get('page', 1, type=int)
    tasks = get_all_tasks(page)
    return render_template('all_tasks.html', tasks=tasks, now=datetime.utcnow(),
                         current_user_id=session['user_id'], is_admin=True)

@app.route('/tasks/public')
@login_required
def public_tasks():
    page = request.args.get('page', 1, type=int)
    tasks = Task.query.filter(
        Task.task_type == 'общая',
        Task.status != 'выполнена'
    ).order_by(Task.created_at.desc()).paginate(
        page=page, per_page=15, error_out=False
    )
    user = User.query.get(session['user_id'])
    return render_template('public_tasks.html', tasks=tasks, now=datetime.utcnow(),
                         current_user_id=session['user_id'], is_admin=user.is_admin)

@app.route('/tasks/archive')
@login_required
def archive_tasks():
    page = request.args.get('page', 1, type=int)
    user = User.query.get(session['user_id'])

    if user.is_admin:
        # Админы видят все выполненные задачи
        tasks = get_archived_tasks(page)
    else:
        # Сотрудники видят только свои выполненные задачи
        tasks = get_user_archived_tasks(session['user_id'], page)

    return render_template('archive.html', tasks=tasks, now=datetime.utcnow(),
                         current_user_id=session['user_id'], is_admin=user.is_admin)

@app.route('/tasks/<int:task_id>/status', methods=['POST'])
@login_required
def update_task_status_route(task_id):
    new_status = request.form.get('status')
    update_task_status(task_id, new_status)
    return redirect(request.referrer or url_for('index'))

@app.route('/tasks/<int:task_id>/approve', methods=['POST'])
@admin_required
def approve_task(task_id):
    update_task_status(task_id, 'выполнена')
    return redirect(request.referrer or url_for('all_tasks'))

@app.route('/tasks/<int:task_id>/reject', methods=['POST'])
@admin_required
def reject_task(task_id):
    update_task_status(task_id, 'в процессе')
    return redirect(request.referrer or url_for('all_tasks'))

@app.route('/api/tasks/updates')
@login_required
def get_task_updates():
    """API endpoint для получения обновлений задач (исключая выполненные)"""
    user = User.query.get(session['user_id'])

    if user.is_admin:
        # Админы получают все активные задачи (исключая выполненные)
        tasks = Task.query.filter(Task.status != 'выполнена').order_by(Task.created_at.desc()).all()
    else:
        # Сотрудники получают только свои активные задачи (исключая выполненные)
        tasks = Task.query.filter(
            Task.assignee_id == session['user_id'],
            Task.status != 'выполнена'
        ).order_by(Task.created_at.desc()).all()

    tasks_data = []
    for task in tasks:
        task_data = {
            'id': task.id,
            'title': task.title,
            'status': task.status,
            'assignee_id': task.assignee_id,
            'assignee_name': task.assignee.name,
            'creator_name': task.creator.name,
            'deadline': task.deadline.strftime('%d.%m.%Y %H:%M'),
            'created_at': task.created_at.strftime('%d.%m.%Y %H:%M'),
            'task_type': task.task_type,
            'is_overdue': task.deadline < datetime.utcnow()
        }
        tasks_data.append(task_data)

    # Также отправляем статистику для админов
    employees_stats = []
    if user.is_admin:
        all_users = User.query.order_by(User.is_admin.desc(), User.name).all()
        for employee in all_users:
            if not employee.is_admin:
                active_tasks = Task.query.filter(
                    Task.assignee_id == employee.id,
                    Task.status.in_(['ждет принятия задачи', 'в процессе', 'проверяется админом'])
                ).count()

                completed_tasks = Task.query.filter(
                    Task.assignee_id == employee.id,
                    Task.status == 'выполнена'
                ).count()

                employees_stats.append({
                    'id': employee.id,
                    'name': employee.name,
                    'active_tasks_count': active_tasks,
                    'completed_tasks_count': completed_tasks
                })

    return jsonify({
        'tasks': tasks_data,
        'employees_stats': employees_stats,
        'current_user_id': session['user_id'],
        'is_admin': user.is_admin,
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/tasks/<int:task_id>/delete', methods=['POST'])
@admin_required
def delete_task_route(task_id):
    if delete_task(task_id):
        flash('Задача удалена!', 'success')
    else:
        flash('Ошибка при удалении задачи', 'danger')
    return redirect(request.referrer or url_for('all_tasks'))

@app.route('/tasks/<int:task_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_task(task_id):
    task = Task.query.get_or_404(task_id)
    if request.method == 'POST':
        task.title = request.form.get('title')
        task.description = request.form.get('description')
        task.assignee_id = request.form.get('assignee_id')
        task.deadline = datetime.strptime(request.form.get('deadline'), '%Y-%m-%dT%H:%M')
        task.task_type = request.form.get('task_type')

        try:
            db.session.commit()
            flash('Задача успешно обновлена!', 'success')
            return redirect(url_for('all_tasks'))
        except Exception as e:
            db.session.rollback()
            flash('Ошибка при обновлении задачи', 'danger')
            print(f"Error: {e}")

    employees = User.query.filter_by(is_admin=False).all()
    return render_template('edit_task.html', task=task, employees=employees)

# Маршруты для работы с фотографиями профиля
@app.route('/user/<int:user_id>/upload_photo', methods=['POST'])
@login_required
def upload_profile_photo(user_id):
    # Проверяем, что пользователь может загружать фото только для себя или админ для любого
    current_user = User.query.get(session['user_id'])
    if not current_user.is_admin and session['user_id'] != user_id:
        flash('Доступ запрещен', 'danger')
        return redirect(url_for('index'))

    if 'photo' not in request.files:
        flash('Файл не выбран', 'danger')
        return redirect(url_for('index'))

    file = request.files['photo']
    if file.filename == '':
        flash('Файл не выбран', 'danger')
        return redirect(url_for('index'))

    if file and allowed_file(file.filename):
        try:
            # Получаем пользователя
            user = User.query.get_or_404(user_id)

            # Удаляем старое фото если есть
            if user.profile_photo:
                old_photo_path = os.path.join(app.config['UPLOAD_FOLDER'], user.profile_photo)
                if os.path.exists(old_photo_path):
                    os.remove(old_photo_path)

            # Создаем уникальное имя файла
            filename = secure_filename(file.filename)
            file_extension = filename.rsplit('.', 1)[1].lower()
            unique_filename = f"user_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"

            # Сохраняем файл
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)

            # Обрабатываем изображение (изменяем размер)
            with Image.open(file_path) as img:
                # Конвертируем в RGB если нужно
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Изменяем размер до 200x200 с сохранением пропорций
                img.thumbnail((200, 200), Image.Resampling.LANCZOS)

                # Создаем квадратное изображение
                width, height = img.size
                if width != height:
                    # Создаем квадратный фон
                    new_img = Image.new('RGB', (200, 200), (255, 255, 255))
                    # Вставляем изображение по центру
                    offset = ((200 - width) // 2, (200 - height) // 2)
                    new_img.paste(img, offset)
                    img = new_img

                # Сохраняем обработанное изображение
                img.save(file_path, 'JPEG', quality=85)

            # Обновляем запись в базе данных
            user.profile_photo = unique_filename
            db.session.commit()

            flash('Фотография успешно загружена!', 'success')

        except Exception as e:
            flash('Ошибка при загрузке фотографии', 'danger')
            print(f"Error uploading photo: {e}")
    else:
        flash('Недопустимый формат файла. Разрешены только JPG, JPEG, PNG', 'danger')

    return redirect(url_for('index'))

@app.route('/user/<int:user_id>/delete_photo', methods=['POST'])
@login_required
def delete_profile_photo(user_id):
    # Проверяем, что пользователь может удалять фото только для себя или админ для любого
    current_user = User.query.get(session['user_id'])
    if not current_user.is_admin and session['user_id'] != user_id:
        flash('Доступ запрещен', 'danger')
        return redirect(url_for('index'))

    try:
        user = User.query.get_or_404(user_id)

        if user.profile_photo:
            # Удаляем файл
            photo_path = os.path.join(app.config['UPLOAD_FOLDER'], user.profile_photo)
            if os.path.exists(photo_path):
                os.remove(photo_path)

            # Обновляем запись в базе данных
            user.profile_photo = None
            db.session.commit()

            flash('Фотография удалена!', 'success')
        else:
            flash('У пользователя нет фотографии', 'info')

    except Exception as e:
        flash('Ошибка при удалении фотографии', 'danger')
        print(f"Error deleting photo: {e}")

    return redirect(url_for('index'))

@app.route('/uploads/profile_photos/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    init_db()
    app.run(debug=True, port=5002, host='0.0.0.0')