#!/usr/bin/env python3
"""
Миграция для добавления поля profile_photo в таблицу User
"""

import sqlite3
import os

def migrate_database():
    """Добавляет поле profile_photo в таблицу User если его еще нет"""

    db_path = 'instance/tasks.db'

    if not os.path.exists(db_path):
        print("База данных не найдена. Создайте базу данных сначала.")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Проверяем, существует ли уже поле profile_photo
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'profile_photo' not in columns:
            print("Добавляем поле profile_photo в таблицу user...")
            cursor.execute("ALTER TABLE user ADD COLUMN profile_photo VARCHAR(255)")
            conn.commit()
            print("✅ Поле profile_photo успешно добавлено!")
        else:
            print("ℹ️  Поле profile_photo уже существует в таблице user")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ Ошибка при миграции: {e}")
        return False

if __name__ == "__main__":
    print("🔄 Запуск миграции базы данных...")
    if migrate_database():
        print("✅ Миграция завершена успешно!")
    else:
        print("❌ Миграция не удалась!")
