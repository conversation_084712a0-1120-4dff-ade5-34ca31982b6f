// ===== MINIMAL ELEGANT INTERACTIONS =====

document.addEventListener('DOMContentLoaded', function() {

    // Auto-hide flash messages after 3 seconds
    const flashMessages = document.querySelectorAll('.alert');
    flashMessages.forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-20px)';

            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 3000);
    });

    // Simple form validation feedback
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#ef4444';
                    isValid = false;
                } else {
                    field.style.borderColor = '#e2e8f0';
                }
            });

            if (!isValid) {
                e.preventDefault();
            }
        });
    });

    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // ===== CUSTOM NAVBAR TOGGLER =====

    // Обработка кастомного бургер-меню
    const customToggler = document.querySelector('.custom-navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (customToggler && navbarCollapse) {
        // Обновляем aria-expanded при изменении состояния collapse
        navbarCollapse.addEventListener('shown.bs.collapse', function() {
            customToggler.setAttribute('aria-expanded', 'true');
        });

        navbarCollapse.addEventListener('hidden.bs.collapse', function() {
            customToggler.setAttribute('aria-expanded', 'false');
            // Принудительно убираем все стили после закрытия
            this.style.height = '';
            this.style.overflow = '';
        });

        navbarCollapse.addEventListener('hide.bs.collapse', function() {
            // Начинаем скрывать содержимое сразу при начале закрытия
            const navbarNav = this.querySelector('.navbar-nav');
            if (navbarNav) {
                navbarNav.style.opacity = '0';
                navbarNav.style.transform = 'translateY(-10px) translateZ(0)';
            }
        });

        navbarCollapse.addEventListener('show.bs.collapse', function() {
            // Показываем содержимое при открытии
            const navbarNav = this.querySelector('.navbar-nav');
            if (navbarNav) {
                navbarNav.style.opacity = '1';
                navbarNav.style.transform = 'translateZ(0)';
            }
        });
    }

    // ===== REAL-TIME UPDATES POLLING =====

    // Запускаем polling только на страницах с активными задачами (не в архиве)
    if ((document.querySelector('[data-task-id]') || document.querySelector('.table')) &&
        !document.querySelector('.archive-task-card')) {
        startPolling();
    }

});

// Функция для запуска polling
function startPolling() {
    // Проверяем обновления каждые 5 секунд
    setInterval(checkForUpdates, 5000);

    console.log('🔄 Polling started - checking for updates every 5 seconds');
}

// Функция для проверки обновлений
async function checkForUpdates() {
    try {
        const response = await fetch('/api/tasks/updates');
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();

        // Обновляем задачи
        updateTasks(data.tasks, data.current_user_id, data.is_admin);

        // Обновляем статистику сотрудников (для админов)
        if (data.is_admin && data.employees_stats.length > 0) {
            updateEmployeeStats(data.employees_stats);
        }

    } catch (error) {
        console.error('Error checking for updates:', error);
    }
}

// Функция для обновления задач
function updateTasks(tasks, currentUserId, isAdmin) {
    tasks.forEach(task => {
        const taskCard = document.querySelector(`[data-task-id="${task.id}"]`);
        if (!taskCard) return;

        // Проверяем изменился ли статус
        const currentStatus = taskCard.getAttribute('data-task-status');
        if (currentStatus !== task.status) {
            // Обновляем статус в data-атрибуте
            taskCard.setAttribute('data-task-status', task.status);

            // Обновляем бейдж статуса
            updateStatusBadge(taskCard, task.status);

            // Обновляем кнопки действий
            updateTaskActions(taskCard, task, currentUserId, isAdmin);
        }

        // Обновляем просроченность
        const isOverdue = task.is_overdue;
        const currentOverdue = taskCard.getAttribute('data-task-overdue') === 'true';
        if (isOverdue !== currentOverdue) {
            taskCard.setAttribute('data-task-overdue', isOverdue);
            if (isOverdue) {
                taskCard.classList.add('border-danger');
            } else {
                taskCard.classList.remove('border-danger');
            }
        }
    });
}

// Функция для обновления бейджа статуса
function updateStatusBadge(taskCard, status) {
    const badge = taskCard.querySelector('.task-status-badge');
    if (!badge) return;

    // Убираем все классы цветов
    badge.classList.remove('bg-warning', 'bg-primary', 'bg-info', 'bg-success');

    // Добавляем нужный класс в зависимости от статуса
    switch (status) {
        case 'ждет принятия задачи':
            badge.classList.add('bg-warning');
            break;
        case 'в процессе':
            badge.classList.add('bg-primary');
            break;
        case 'проверяется админом':
            badge.classList.add('bg-info');
            break;
        case 'выполнена':
            badge.classList.add('bg-success');
            break;
    }

    // Обновляем текст
    badge.textContent = status;
    badge.setAttribute('data-status', status);
}

// Функция для обновления кнопок действий
function updateTaskActions(taskCard, task, currentUserId, isAdmin) {
    const actionsContainer = taskCard.querySelector('.task-actions');
    if (!actionsContainer) return;

    // Генерируем новые кнопки
    let buttonsHTML = '';

    // Кнопка "Принять задачу"
    if (task.status === 'ждет принятия задачи' && task.assignee_id === currentUserId) {
        buttonsHTML += `
            <form method="POST" action="/tasks/${task.id}/status" class="d-inline">
                <input type="hidden" name="status" value="в процессе">
                <button type="submit" class="btn btn-success btn-sm">Принять задачу</button>
            </form>
        `;
    }

    // Кнопка "Отправить на проверку"
    if (task.status === 'в процессе' && task.assignee_id === currentUserId) {
        buttonsHTML += `
            <form method="POST" action="/tasks/${task.id}/status" class="d-inline">
                <input type="hidden" name="status" value="проверяется админом">
                <button type="submit" class="btn btn-primary btn-sm">Отправить на проверку</button>
            </form>
        `;
    }

    // Кнопки для админов при проверке
    if (task.status === 'проверяется админом' && isAdmin) {
        buttonsHTML += `
            <form method="POST" action="/tasks/${task.id}/approve" class="d-inline">
                <button type="submit" class="btn btn-success btn-sm">Принять</button>
            </form>
            <form method="POST" action="/tasks/${task.id}/reject" class="d-inline">
                <button type="submit" class="btn btn-warning btn-sm">Отклонить</button>
            </form>
        `;
    }

    // Кнопки редактирования и удаления для админов
    if (isAdmin) {
        buttonsHTML += `
            <a href="/tasks/${task.id}/edit" class="btn btn-warning btn-sm">Редактировать</a>
            <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal${task.id}">
                Удалить
            </button>
        `;
    }

    // Обновляем содержимое только если оно изменилось
    if (actionsContainer.innerHTML.trim() !== buttonsHTML.trim()) {
        actionsContainer.innerHTML = buttonsHTML;
    }
}

// Функция для обновления статистики сотрудников
function updateEmployeeStats(employeesStats) {
    employeesStats.forEach(employee => {
        // Обновляем в новых красивых карточках
        const employeeCard = document.querySelector(`[data-employee-id="${employee.id}"]`);
        if (employeeCard) {
            const activeCount = employeeCard.querySelector('.active-tasks-count');
            const completedCount = employeeCard.querySelector('.completed-tasks-count');

            if (activeCount) activeCount.textContent = employee.active_tasks_count;
            if (completedCount) completedCount.textContent = employee.completed_tasks_count;

            // Обновляем прогресс-бары
            const totalTasks = employee.active_tasks_count + employee.completed_tasks_count;
            if (totalTasks > 0) {
                const activeProgress = employeeCard.querySelector('.active-progress');
                const completedProgress = employeeCard.querySelector('.completed-progress');

                if (activeProgress) {
                    const activePercentage = (employee.active_tasks_count / totalTasks) * 100;
                    activeProgress.style.width = `${activePercentage}%`;
                }

                if (completedProgress) {
                    const completedPercentage = (employee.completed_tasks_count / totalTasks) * 100;
                    completedProgress.style.width = `${completedPercentage}%`;
                }
            }
        }
    });
}

// ===== PHOTO MODAL FUNCTIONALITY =====
let currentUserId = null;

function openPhotoModal(userId, userName, currentPhotoUrl) {
    currentUserId = userId;

    // Устанавливаем имя пользователя
    document.getElementById('employeeName').textContent = userName;

    // Устанавливаем текущее фото или инициалы
    const photoPreview = document.getElementById('currentPhotoPreview');
    const initialsElement = document.getElementById('currentInitials');
    const deleteSection = document.getElementById('deletePhotoSection');

    if (currentPhotoUrl) {
        photoPreview.src = currentPhotoUrl;
        photoPreview.style.display = 'block';
        initialsElement.style.display = 'none';
        deleteSection.style.display = 'block';
    } else {
        photoPreview.style.display = 'none';
        initialsElement.style.display = 'block';
        // Генерируем инициалы
        const nameParts = userName.split(' ');
        const initials = nameParts.length > 1 ?
            nameParts[0][0] + nameParts[1][0] :
            nameParts[0][0];
        initialsElement.textContent = initials;
        deleteSection.style.display = 'none';
    }

    // Устанавливаем action для форм
    document.getElementById('uploadPhotoForm').action = `/user/${userId}/upload_photo`;
    document.getElementById('deletePhotoForm').action = `/user/${userId}/delete_photo`;

    // Показываем модальное окно
    const modal = new bootstrap.Modal(document.getElementById('photoModal'));
    modal.show();
}

// Обработка предварительного просмотра выбранного файла
document.addEventListener('DOMContentLoaded', function() {
    const photoFileInput = document.getElementById('photoFile');
    if (photoFileInput) {
        photoFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const photoPreview = document.getElementById('currentPhotoPreview');
                    const initialsElement = document.getElementById('currentInitials');

                    photoPreview.src = e.target.result;
                    photoPreview.style.display = 'block';
                    initialsElement.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });
    }
});
