:root {
  /* Light Theme */
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f8fafc;
  --text-primary-light: #1e293b;
  --text-secondary-light: #64748b;
  --accent-light: #0ea5e9;
  --border-light: #e2e8f0;
  --error-light: #ef4444;
  --success-light: #22c55e;
  --warning-light: #f59e0b;
  --hover-light: #f1f5f9;
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.05);

  /* Dark Theme */
  --bg-primary-dark: #0f172a;
  --bg-secondary-dark: #1e293b;
  --text-primary-dark: #f8fafc;
  --text-secondary-dark: #94a3b8;
  --accent-dark: #38bdf8;
  --border-dark: #334155;
  --error-dark: #f87171;
  --success-dark: #4ade80;
  --warning-dark: #fbbf24;
  --hover-dark: #1e293b;
  --shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Default Light Theme */
:root {
  --bg-primary: var(--bg-primary-light);
  --bg-secondary: var(--bg-secondary-light);
  --text-primary: var(--text-primary-light);
  --text-secondary: var(--text-secondary-light);
  --accent: var(--accent-light);
  --border: var(--border-light);
  --error: var(--error-light);
  --success: var(--success-light);
  --warning: var(--warning-light);
  --hover: var(--hover-light);
  --shadow: var(--shadow-light);
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: var(--bg-primary-dark);
  --bg-secondary: var(--bg-secondary-dark);
  --text-primary: var(--text-primary-dark);
  --text-secondary: var(--text-secondary-dark);
  --accent: var(--accent-dark);
  --border: var(--border-dark);
  --error: var(--error-dark);
  --success: var(--success-dark);
  --warning: var(--warning-dark);
  --hover: var(--hover-dark);
  --shadow: var(--shadow-dark);
} 