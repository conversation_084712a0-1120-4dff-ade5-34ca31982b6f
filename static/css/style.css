/* ===== MINIMAL DARK PROFESSIONAL THEME ===== */

/* CSS Variables for Minimal Dark Theme */
:root {
  --bg-primary: #0f0f0f;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #262626;
  --bg-card: #1f1f1f;
  --accent-primary: #2563eb;
  --accent-secondary: #3b82f6;
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --border-light: 1px solid #333333;
  --border-subtle: 1px solid #262626;
  --shadow-minimal: 0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-card: 0 4px 12px rgba(0, 0, 0, 0.3);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --info-color: #0891b2;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  font-size: 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 { font-size: 2rem; font-weight: 700; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Container */
.container {
  max-width: 1200px;
}

/* Navigation */
.navbar {
  background: var(--bg-secondary) !important;
  border-bottom: var(--border-light);
  box-shadow: var(--shadow-subtle);
  padding: 1rem 0;
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: auto;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--text-primary) !important;
  text-decoration: none !important;
}

.nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  transition: color 0.2s ease;
  padding: 0.5rem 1rem !important;
  border-radius: var(--radius-sm);
}

.nav-link:hover {
  color: var(--accent-primary) !important;
  background: var(--bg-tertiary);
}

/* ===== CUSTOM BURGER MENU (NO GLITCHES) ===== */
.custom-navbar-toggler {
  border: none;
  background: transparent;
  padding: 0.5rem;
  cursor: pointer;
  outline: none;
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
}

.custom-navbar-toggler:hover {
  background: var(--bg-tertiary);
}

.custom-navbar-toggler:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.custom-toggler-icon {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  justify-content: space-between;
  position: relative;
}

.custom-toggler-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background: var(--text-primary);
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

/* Анимация при открытии */
.custom-navbar-toggler[aria-expanded="true"] .custom-toggler-icon span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.custom-navbar-toggler[aria-expanded="true"] .custom-toggler-icon span:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.custom-navbar-toggler[aria-expanded="true"] .custom-toggler-icon span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== FIXED NAVBAR COLLAPSE (NO MORE GLITCH) ===== */
.navbar-collapse {
  transition: none !important;
  overflow: hidden;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

.navbar-collapse.collapsing {
  transition: height 0.25s ease-out !important;
  overflow: hidden;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

.navbar-collapse.show {
  transition: none !important;
  overflow: visible;
  background: transparent !important;
}

/* Prevent layout shifts during collapse */
.navbar-nav {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Performance optimization for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .navbar-collapse,
  .navbar-collapse.collapsing,
  .navbar-nav .nav-link {
    transition: none !important;
    animation: none !important;
  }
}

/* ===== AUTH PAGES STYLING ===== */

/* Center auth pages vertically and horizontally */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.auth-card {
  background: var(--bg-card);
  border: var(--border-subtle);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
}

.auth-card .card-header {
  background: var(--bg-tertiary);
  border-bottom: var(--border-subtle);
  padding: 1.5rem;
  text-align: center;
}

.auth-card .card-body {
  padding: 2rem;
}

.auth-card .card-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-subtle);
  padding: 1rem 2rem;
}

.auth-welcome {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-welcome h2 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
  letter-spacing: -0.02em;
}

.auth-welcome p {
  color: var(--text-muted);
  font-size: 0.95rem;
}

/* Mobile navbar styling */
@media (max-width: 991.98px) {
  .navbar-collapse {
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
    transform: translateZ(0);
  }

  .navbar-nav {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--radius-sm);
    margin-top: 1rem;
    border: var(--border-light);
    box-shadow: var(--shadow-medium);
    transform: translateZ(0);
    transition: opacity 0.2s ease, transform 0.2s ease;
    opacity: 1;
  }

  .navbar-collapse.collapsing .navbar-nav {
    opacity: 0;
    transform: translateY(-10px) translateZ(0);
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem !important;
    margin: 0.125rem 0;
    border-radius: var(--radius-sm);
    transition: background-color 0.15s ease, color 0.15s ease;
    will-change: background-color;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .navbar-nav .nav-link:hover {
    background: var(--bg-secondary);
    color: var(--accent-primary) !important;
  }

  .navbar-nav .nav-link:active {
    transform: scale(0.98);
  }
}

/* ===== MINIMAL CARDS ===== */
.card {
  background: var(--bg-card);
  border: var(--border-subtle);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  transition: all 0.15s ease;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  border-color: var(--accent-primary);
}

.card-header {
  background: var(--bg-card);
  border-bottom: var(--border-subtle);
  border-radius: 0;
  padding: 1.25rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
  background: var(--bg-card);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-subtle);
  padding: 1rem 1.5rem;
}

.card-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  letter-spacing: -0.01em;
}

.card-text {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* ===== MINIMAL BUTTONS ===== */
.btn {
  border-radius: var(--radius-sm);
  font-weight: 500;
  padding: 0.6rem 1.2rem;
  border: 1px solid transparent;
  transition: all 0.15s ease;
  font-size: 0.875rem;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.btn-primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
  color: white;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-light);
}

.btn-secondary:hover {
  background: var(--accent-secondary);
  color: white;
  border-color: var(--accent-secondary);
}

.btn-success {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn-success:hover {
  background: #047857;
  border-color: #047857;
  color: white;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background: #b45309;
  border-color: #b45309;
  color: white;
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background: #b91c1c;
  border-color: #b91c1c;
  color: white;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

/* Forms */
.form-control, .form-select {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  background: var(--bg-secondary);
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  color: var(--text-primary);
  outline: none;
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Badges */
.badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.5;
}

.bg-warning {
  background: var(--warning-color) !important;
  color: white !important;
}

.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
}

.bg-success {
  background: var(--success-color) !important;
  color: white !important;
}

.bg-info {
  background: var(--info-color) !important;
  color: white !important;
}

/* Task status badge styling */
.task-status-badge {
  transition: all 0.3s ease;
}

.task-status-badge.bg-warning {
  background: var(--warning-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.task-status-badge.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.task-status-badge.bg-info {
  background: var(--info-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.task-status-badge.bg-success {
  background: var(--success-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

/* Alerts */
.alert {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  border-left: 3px solid var(--accent-primary);
  padding: 1rem;
}

.alert-info {
  border-left-color: var(--accent-primary);
  background: rgba(59, 130, 246, 0.1);
}

.alert-success {
  border-left-color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.alert-warning {
  border-left-color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
}

.alert-danger {
  border-left-color: var(--danger-color);
  background: rgba(239, 68, 68, 0.1);
}

/* Modal */
.modal-content {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-medium);
}

.modal-header {
  background: var(--bg-secondary);
  border-bottom: var(--border-light);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.modal-title {
  color: var(--text-primary);
  font-weight: 600;
}

.modal-body {
  color: var(--text-secondary);
}

.modal-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-light);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Pagination */
.pagination {
  margin-top: 2rem;
}

.page-link {
  background: var(--bg-secondary);
  border: var(--border-light);
  color: var(--text-secondary);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.page-link:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.page-item.active .page-link {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

/* Special Effects */
.border-danger {
  border-color: #ef4444 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  /* Enhanced custom burger menu on mobile */
  .custom-navbar-toggler {
    padding: 0.75rem;
  }

  .custom-toggler-icon {
    width: 28px;
    height: 20px;
  }

  .custom-toggler-icon span {
    height: 3px;
  }
}

/* Utility Classes */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* ===== MINIMAL TABLES ===== */
.table {
  margin-bottom: 0;
  background: var(--bg-card);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.table th {
  background: var(--bg-tertiary);
  border-bottom: var(--border-subtle);
  font-weight: 600;
  color: var(--text-primary);
  padding: 1.2rem 1rem;
  font-size: 0.875rem;
  letter-spacing: -0.01em;
  border-top: none;
}

.table td {
  padding: 1.2rem 1rem;
  border-bottom: var(--border-subtle);
  vertical-align: middle;
  color: var(--text-secondary);
  border-top: none;
}

.table-hover tbody tr:hover {
  background: var(--bg-tertiary);
  transition: background-color 0.15s ease;
}

.table-responsive {
  border-radius: var(--radius-md);
  overflow: hidden;
  border: var(--border-subtle);
}

/* Admin Cards Styling */
.border-warning {
  border-color: #f59e0b !important;
}

.table tbody tr:has(.badge.bg-warning) {
  background: rgba(245, 158, 11, 0.1);
}

.table tbody tr:has(.badge.bg-warning):hover {
  background: rgba(245, 158, 11, 0.15);
}

/* Mobile Employee Cards */
@media (max-width: 767.98px) {
  .card .card {
    border: var(--border-light);
    box-shadow: var(--shadow-subtle);
  }

  .card .card:hover {
    transform: none;
    box-shadow: var(--shadow-medium);
  }

  .card .card.border-warning {
    background: rgba(245, 158, 11, 0.1);
  }
}

/* Custom Scrollbar for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}

/* Additional Dark Theme Enhancements */
.border-danger {
  border-color: var(--danger-color) !important;
  box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3);
}

/* Enhanced focus states for dark theme */
.btn:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Dark theme specific hover effects */
.navbar-brand:hover {
  color: var(--accent-primary) !important;
}

/* Better contrast for text elements */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* Bootstrap overrides for dark theme */
.dropdown-menu {
  background: var(--bg-secondary);
  border: var(--border-light);
  box-shadow: var(--shadow-medium);
}

.dropdown-item {
  color: var(--text-secondary);
}

.dropdown-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Dark theme for close buttons */
.btn-close {
  filter: invert(1);
}

/* Dark theme for modals backdrop */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Enhanced table styling for dark theme */
.table {
  color: var(--text-secondary);
}

.table th {
  color: var(--text-primary);
  border-color: var(--border-light);
}

.table td {
  border-color: var(--border-light);
}

/* Dark theme for pagination */
.page-item.disabled .page-link {
  background: var(--bg-tertiary);
  border-color: var(--border-light);
  color: var(--text-muted);
}

/* Professional glow effects for interactive elements */
.card:hover,
.btn:hover {
  transition: all 0.3s ease;
}

/* Dark theme for form validation */
.form-control.is-invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

/* ===== MINIMAL EMPLOYEES GRID ===== */

.employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.employee-card {
  background: var(--bg-card);
  border-radius: var(--radius-md);
  padding: 2rem;
  border: var(--border-subtle);
  box-shadow: var(--shadow-card);
  transition: all 0.2s ease;
  position: relative;
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  border-color: var(--accent-primary);
}

.admin-card {
  border-left: 3px solid var(--warning-color);
}

.admin-card:hover {
  border-left-color: var(--warning-color);
  box-shadow: 0 8px 25px rgba(217, 119, 6, 0.2);
}

/* ===== MINIMAL EMPLOYEE AVATAR ===== */
.employee-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.avatar-circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.avatar-circle.employee-avatar {
  background: var(--accent-primary);
  border: 2px solid var(--accent-secondary);
}

.avatar-circle.admin-avatar {
  background: var(--warning-color);
  border: 2px solid #fbbf24;
}

.avatar-initials {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  letter-spacing: -0.02em;
}

.admin-crown {
  position: absolute;
  top: -8px;
  right: -5px;
  font-size: 1.2rem;
  color: var(--warning-color);
}

/* Employee Info */
.employee-info {
  text-align: center;
  margin-bottom: 2rem;
}

.employee-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.employee-phone {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  font-family: 'Courier New', monospace;
}

.employee-role {
  margin-top: 1rem;
}

.role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.role-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.role-badge:hover::before {
  left: 100%;
}

.admin-role {
  background: linear-gradient(135deg, var(--warning-color), #fbbf24);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.employee-role .role-badge {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.role-icon {
  font-size: 1rem;
}

/* Employee Stats */
.employee-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.active-stat .stat-circle {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.completed-stat .stat-circle {
  background: linear-gradient(135deg, var(--success-color), #059669);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.stat-progress {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 1s ease-in-out;
  position: relative;
  overflow: hidden;
}

.active-progress {
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
}

.completed-progress {
  background: linear-gradient(90deg, var(--success-color), #059669);
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.employee-card:hover .stat-circle {
  transform: scale(1.1);
}

/* Admin Stats */
.admin-stats {
  margin-bottom: 2rem;
  text-align: center;
}

.admin-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #1e1b4b, #312e81);
  border-radius: 15px;
  color: var(--warning-color);
  font-weight: 600;
  border: 1px solid var(--warning-color);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
}

.admin-icon {
  font-size: 1.2rem;
}

/* Employee Actions */
.employee-actions {
  text-align: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
  color: white;
  text-decoration: none;
}

.action-btn:hover::before {
  left: 100%;
}

.btn-icon {
  font-size: 1rem;
}

.admin-actions {
  padding: 1rem;
}

.admin-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-style: italic;
}

.status-icon {
  font-size: 1rem;
}

/* Responsive Design for Employee Cards */
@media (max-width: 768px) {
  .employees-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 1rem;
  }

  .employee-card {
    padding: 1.5rem;
  }

  .employee-stats {
    gap: 1rem;
  }

  .stat-circle {
    width: 50px;
    height: 50px;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .avatar-circle {
    width: 70px;
    height: 70px;
  }

  .avatar-initials {
    font-size: 1.5rem;
  }

  .employee-name {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .employees-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .employee-card {
    padding: 1rem;
    border-radius: 15px;
  }

  .employee-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
  }

  .stat-circle {
    width: 45px;
    height: 45px;
    margin: 0;
  }

  .stat-number {
    font-size: 1.1rem;
  }
}

/* Remove excessive animations for minimal design */

/* ===== ARCHIVE TASKS STYLING ===== */

.archive-task-card {
  opacity: 0.9;
  transition: all 0.3s ease;
  border-left: 4px solid var(--success-color);
}

.archive-task-card:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.archive-task-card .card-header {
  background: linear-gradient(135deg, var(--bg-secondary), rgba(16, 185, 129, 0.1));
}

.archive-task-card .card-footer {
  border-top: 1px solid rgba(16, 185, 129, 0.3);
}

.archive-stats .badge {
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Archive page specific styling */
.archive-task-card .task-status-badge {
  background: linear-gradient(135deg, var(--success-color), #059669) !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.6);
  }
}