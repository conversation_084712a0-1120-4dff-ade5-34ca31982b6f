/* ===== DARK PROFESSIONAL THEME ===== */

/* CSS Variables for Dark Professional Theme */
:root {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #374151;
  --accent-primary: #3b82f6;
  --accent-secondary: #6366f1;
  --text-primary: #ffffff;
  --text-secondary: #e5e5e5;
  --text-muted: #9ca3af;
  --border-light: 1px solid #4b5563;
  --border-focus: 1px solid #3b82f6;
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.4);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  font-size: 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 { font-size: 2rem; font-weight: 700; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Container */
.container {
  max-width: 1200px;
}

/* Navigation */
.navbar {
  background: var(--bg-secondary) !important;
  border-bottom: var(--border-light);
  box-shadow: var(--shadow-subtle);
  padding: 1rem 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--text-primary) !important;
  text-decoration: none !important;
}

.nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  transition: color 0.2s ease;
  padding: 0.5rem 1rem !important;
  border-radius: var(--radius-sm);
}

.nav-link:hover {
  color: var(--accent-primary) !important;
  background: var(--bg-tertiary);
}

/* Navbar Toggler (Burger Menu) for Dark Theme */
.navbar-toggler {
  border: 1px solid var(--text-secondary) !important;
  border-radius: var(--radius-sm);
  padding: 0.375rem 0.5rem;
  transition: all 0.2s ease;
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
  border-color: var(--accent-primary) !important;
}

.navbar-toggler:hover {
  border-color: var(--accent-primary) !important;
  background: var(--bg-tertiary);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
  width: 1.5rem;
  height: 1.5rem;
}

/* Navbar collapse styling for dark theme */
.navbar-collapse {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  margin-top: 0.5rem;
}

@media (max-width: 991.98px) {
  .navbar-collapse {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--radius-sm);
    margin-top: 1rem;
    border: var(--border-light);
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem !important;
    margin: 0.25rem 0;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
  }

  .navbar-nav .nav-link:hover {
    background: var(--bg-secondary);
    transform: translateX(5px);
  }
}

/* Cards */
.card {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-medium);
  transition: all 0.2s ease;
  margin-bottom: 1.5rem;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
  border-color: var(--accent-primary);
}

.card-header {
  background: var(--bg-secondary);
  border-bottom: var(--border-light);
  border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
  padding: 1.25rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
  background: var(--bg-secondary);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-light);
  padding: 1rem 1.5rem;
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

.card-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.card-text {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 0;
}

/* Buttons */
.btn {
  border-radius: var(--radius-sm);
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  line-height: 1.5;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.btn-primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
  color: white;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-light);
}

.btn-secondary:hover {
  background: var(--accent-secondary);
  color: white;
  border-color: var(--accent-secondary);
  transform: translateY(-1px);
}

.btn-success {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.btn-success:hover {
  background: #059669;
  border-color: #059669;
  color: white;
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background: #d97706;
  border-color: #d97706;
  color: white;
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background: #dc2626;
  border-color: #dc2626;
  color: white;
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

/* Forms */
.form-control, .form-select {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  background: var(--bg-secondary);
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  color: var(--text-primary);
  outline: none;
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Badges */
.badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.5;
}

.bg-warning {
  background: var(--warning-color) !important;
  color: white !important;
}

.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
}

.bg-success {
  background: var(--success-color) !important;
  color: white !important;
}

.bg-info {
  background: var(--info-color) !important;
  color: white !important;
}

/* Task status badge styling */
.task-status-badge {
  transition: all 0.3s ease;
}

.task-status-badge.bg-warning {
  background: var(--warning-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.task-status-badge.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.task-status-badge.bg-info {
  background: var(--info-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.task-status-badge.bg-success {
  background: var(--success-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

/* Alerts */
.alert {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  border-left: 3px solid var(--accent-primary);
  padding: 1rem;
}

.alert-info {
  border-left-color: var(--accent-primary);
  background: rgba(59, 130, 246, 0.1);
}

.alert-success {
  border-left-color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.alert-warning {
  border-left-color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
}

.alert-danger {
  border-left-color: var(--danger-color);
  background: rgba(239, 68, 68, 0.1);
}

/* Modal */
.modal-content {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-medium);
}

.modal-header {
  background: var(--bg-secondary);
  border-bottom: var(--border-light);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.modal-title {
  color: var(--text-primary);
  font-weight: 600;
}

.modal-body {
  color: var(--text-secondary);
}

.modal-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-light);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Pagination */
.pagination {
  margin-top: 2rem;
}

.page-link {
  background: var(--bg-secondary);
  border: var(--border-light);
  color: var(--text-secondary);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.page-link:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.page-item.active .page-link {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

/* Special Effects */
.border-danger {
  border-color: #ef4444 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  /* Enhanced burger menu visibility on mobile */
  .navbar-toggler {
    border: 2px solid var(--text-secondary) !important;
    padding: 0.5rem 0.75rem;
  }

  .navbar-toggler:hover,
  .navbar-toggler:focus {
    border-color: var(--accent-primary) !important;
    background: rgba(59, 130, 246, 0.1);
  }

  .navbar-toggler-icon {
    width: 1.75rem;
    height: 1.75rem;
  }
}

/* Utility Classes */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* Tables */
.table {
  margin-bottom: 0;
}

.table th {
  background: var(--bg-tertiary);
  border-bottom: var(--border-light);
  font-weight: 600;
  color: var(--text-primary);
  padding: 1rem 0.75rem;
  font-size: 0.875rem;
}

.table td {
  padding: 1rem 0.75rem;
  border-bottom: var(--border-light);
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background: var(--bg-tertiary);
}

.table-responsive {
  border-radius: var(--radius-sm);
  overflow: hidden;
}

/* Admin Cards Styling */
.border-warning {
  border-color: #f59e0b !important;
}

.table tbody tr:has(.badge.bg-warning) {
  background: rgba(245, 158, 11, 0.1);
}

.table tbody tr:has(.badge.bg-warning):hover {
  background: rgba(245, 158, 11, 0.15);
}

/* Mobile Employee Cards */
@media (max-width: 767.98px) {
  .card .card {
    border: var(--border-light);
    box-shadow: var(--shadow-subtle);
  }

  .card .card:hover {
    transform: none;
    box-shadow: var(--shadow-medium);
  }

  .card .card.border-warning {
    background: rgba(245, 158, 11, 0.1);
  }
}

/* Custom Scrollbar for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}

/* Additional Dark Theme Enhancements */
.border-danger {
  border-color: var(--danger-color) !important;
  box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3);
}

/* Enhanced focus states for dark theme */
.btn:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Dark theme specific hover effects */
.navbar-brand:hover {
  color: var(--accent-primary) !important;
}

/* Better contrast for text elements */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* Bootstrap overrides for dark theme */
.dropdown-menu {
  background: var(--bg-secondary);
  border: var(--border-light);
  box-shadow: var(--shadow-medium);
}

.dropdown-item {
  color: var(--text-secondary);
}

.dropdown-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Dark theme for close buttons */
.btn-close {
  filter: invert(1);
}

/* Dark theme for modals backdrop */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Enhanced table styling for dark theme */
.table {
  color: var(--text-secondary);
}

.table th {
  color: var(--text-primary);
  border-color: var(--border-light);
}

.table td {
  border-color: var(--border-light);
}

/* Dark theme for pagination */
.page-item.disabled .page-link {
  background: var(--bg-tertiary);
  border-color: var(--border-light);
  color: var(--text-muted);
}

/* Professional glow effects for interactive elements */
.card:hover,
.btn:hover {
  transition: all 0.3s ease;
}

/* Dark theme for form validation */
.form-control.is-invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

/* ===== BEAUTIFUL EMPLOYEES GRID ===== */

.employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.employee-card {
  background: linear-gradient(145deg, var(--bg-secondary), var(--bg-tertiary));
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.employee-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.employee-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
  border-color: var(--accent-primary);
}

.employee-card:hover::before {
  opacity: 1;
}

.admin-card {
  background: linear-gradient(145deg, #2d1b69, #1e1b4b);
  border: 1px solid var(--warning-color);
}

.admin-card::before {
  background: linear-gradient(90deg, var(--warning-color), #fbbf24);
}

/* Employee Avatar */
.employee-avatar {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.avatar-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.avatar-circle.employee-avatar {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.avatar-circle.admin-avatar {
  background: linear-gradient(135deg, var(--warning-color), #fbbf24);
  box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
}

.avatar-initials {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.admin-crown {
  position: absolute;
  top: -10px;
  right: -5px;
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.employee-card:hover .avatar-circle {
  transform: scale(1.1) rotate(5deg);
}

/* Employee Info */
.employee-info {
  text-align: center;
  margin-bottom: 2rem;
}

.employee-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--text-primary), var(--accent-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.employee-phone {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  font-family: 'Courier New', monospace;
}

.employee-role {
  margin-top: 1rem;
}

.role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.role-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.role-badge:hover::before {
  left: 100%;
}

.admin-role {
  background: linear-gradient(135deg, var(--warning-color), #fbbf24);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.employee-role .role-badge {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.role-icon {
  font-size: 1rem;
}

/* Employee Stats */
.employee-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.active-stat .stat-circle {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.completed-stat .stat-circle {
  background: linear-gradient(135deg, var(--success-color), #059669);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.stat-progress {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 1s ease-in-out;
  position: relative;
  overflow: hidden;
}

.active-progress {
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
}

.completed-progress {
  background: linear-gradient(90deg, var(--success-color), #059669);
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.employee-card:hover .stat-circle {
  transform: scale(1.1);
}

/* Admin Stats */
.admin-stats {
  margin-bottom: 2rem;
  text-align: center;
}

.admin-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #1e1b4b, #312e81);
  border-radius: 15px;
  color: var(--warning-color);
  font-weight: 600;
  border: 1px solid var(--warning-color);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
}

.admin-icon {
  font-size: 1.2rem;
}

/* Employee Actions */
.employee-actions {
  text-align: center;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
  color: white;
  text-decoration: none;
}

.action-btn:hover::before {
  left: 100%;
}

.btn-icon {
  font-size: 1rem;
}

.admin-actions {
  padding: 1rem;
}

.admin-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-style: italic;
}

.status-icon {
  font-size: 1rem;
}

/* Responsive Design for Employee Cards */
@media (max-width: 768px) {
  .employees-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 1rem;
  }

  .employee-card {
    padding: 1.5rem;
  }

  .employee-stats {
    gap: 1rem;
  }

  .stat-circle {
    width: 50px;
    height: 50px;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .avatar-circle {
    width: 70px;
    height: 70px;
  }

  .avatar-initials {
    font-size: 1.5rem;
  }

  .employee-name {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .employees-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .employee-card {
    padding: 1rem;
    border-radius: 15px;
  }

  .employee-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
  }

  .stat-circle {
    width: 45px;
    height: 45px;
    margin: 0;
  }

  .stat-number {
    font-size: 1.1rem;
  }
}

/* Animation delays for staggered effect */
.employee-card:nth-child(1) { animation-delay: 0.1s; }
.employee-card:nth-child(2) { animation-delay: 0.2s; }
.employee-card:nth-child(3) { animation-delay: 0.3s; }
.employee-card:nth-child(4) { animation-delay: 0.4s; }
.employee-card:nth-child(5) { animation-delay: 0.5s; }
.employee-card:nth-child(6) { animation-delay: 0.6s; }

/* Fade in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.employee-card {
  animation: fadeInUp 0.6s ease-out both;
}

/* ===== ARCHIVE TASKS STYLING ===== */

.archive-task-card {
  opacity: 0.9;
  transition: all 0.3s ease;
  border-left: 4px solid var(--success-color);
}

.archive-task-card:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.archive-task-card .card-header {
  background: linear-gradient(135deg, var(--bg-secondary), rgba(16, 185, 129, 0.1));
}

.archive-task-card .card-footer {
  border-top: 1px solid rgba(16, 185, 129, 0.3);
}

.archive-stats .badge {
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Archive page specific styling */
.archive-task-card .task-status-badge {
  background: linear-gradient(135deg, var(--success-color), #059669) !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.6);
  }
}