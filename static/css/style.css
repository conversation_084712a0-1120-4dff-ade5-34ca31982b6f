/* ===== MINIMAL DARK PROFESSIONAL THEME ===== */

/* CSS Variables for Minimal Dark Theme */
:root {
  --bg-primary: #0f0f0f;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #262626;
  --bg-card: #1f1f1f;
  --accent-primary: #2563eb;
  --accent-secondary: #3b82f6;
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --border-light: 1px solid #333333;
  --border-subtle: 1px solid #262626;
  --shadow-minimal: 0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-card: 0 4px 12px rgba(0, 0, 0, 0.3);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --info-color: #0891b2;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  font-size: 15px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 { font-size: 2rem; font-weight: 700; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Container */
.container {
  max-width: 1200px;
}

/* Navigation */
.navbar {
  background: var(--bg-secondary) !important;
  border-bottom: var(--border-light);
  box-shadow: var(--shadow-subtle);
  padding: 1rem 0;
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: auto;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--text-primary) !important;
  text-decoration: none !important;
}

.nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  transition: color 0.2s ease;
  padding: 0.5rem 1rem !important;
  border-radius: var(--radius-sm);
}

.nav-link:hover {
  color: var(--accent-primary) !important;
  background: var(--bg-tertiary);
}

/* ===== CUSTOM BURGER MENU (ONLY ON MOBILE) ===== */
.custom-navbar-toggler {
  border: none;
  background: transparent;
  padding: 0.5rem;
  cursor: pointer;
  outline: none;
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
  display: none; /* Hide on desktop by default */
}

/* Show burger menu only on mobile */
@media (max-width: 991.98px) {
  .custom-navbar-toggler {
    display: block;
  }
}

.custom-navbar-toggler:hover {
  background: var(--bg-tertiary);
}

.custom-navbar-toggler:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.custom-toggler-icon {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  justify-content: space-between;
  position: relative;
}

.custom-toggler-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background: var(--text-primary);
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

/* Анимация при открытии */
.custom-navbar-toggler[aria-expanded="true"] .custom-toggler-icon span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.custom-navbar-toggler[aria-expanded="true"] .custom-toggler-icon span:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.custom-navbar-toggler[aria-expanded="true"] .custom-toggler-icon span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== FIXED NAVBAR COLLAPSE (NO MORE GLITCH) ===== */
.navbar-collapse {
  transition: none !important;
  overflow: hidden;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

.navbar-collapse.collapsing {
  transition: height 0.25s ease-out !important;
  overflow: hidden;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

.navbar-collapse.show {
  transition: none !important;
  overflow: visible;
  background: transparent !important;
}

/* Prevent layout shifts during collapse */
.navbar-nav {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Performance optimization for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .navbar-collapse,
  .navbar-collapse.collapsing,
  .navbar-nav .nav-link {
    transition: none !important;
    animation: none !important;
  }
}

/* ===== AUTH PAGES STYLING ===== */

/* Center auth pages vertically and horizontally */
.auth-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

.auth-card {
  background: var(--bg-card);
  border: var(--border-subtle);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
}

.auth-card .card-header {
  background: var(--bg-tertiary);
  border-bottom: var(--border-subtle);
  padding: 1.5rem;
  text-align: center;
}

.auth-card .card-body {
  padding: 2rem;
}

.auth-card .card-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-subtle);
  padding: 1rem 2rem;
}

.auth-welcome {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-welcome h2 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
  letter-spacing: -0.02em;
}

.auth-welcome p {
  color: var(--text-muted);
  font-size: 0.95rem;
}

/* Mobile navbar styling */
@media (max-width: 991.98px) {
  .navbar-collapse {
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
    transform: translateZ(0);
  }

  .navbar-nav {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--radius-sm);
    margin-top: 1rem;
    border: var(--border-light);
    box-shadow: var(--shadow-medium);
    transform: translateZ(0);
    transition: opacity 0.2s ease, transform 0.2s ease;
    opacity: 1;
  }

  .navbar-collapse.collapsing .navbar-nav {
    opacity: 0;
    transform: translateY(-10px) translateZ(0);
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem !important;
    margin: 0.125rem 0;
    border-radius: var(--radius-sm);
    transition: background-color 0.15s ease, color 0.15s ease;
    will-change: background-color;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  .navbar-nav .nav-link:hover {
    background: var(--bg-secondary);
    color: var(--accent-primary) !important;
  }

  .navbar-nav .nav-link:active {
    transform: scale(0.98);
  }
}

/* ===== MINIMAL CARDS ===== */
.card {
  background: var(--bg-card);
  border: var(--border-subtle);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-card);
  transition: all 0.15s ease;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  border-color: var(--accent-primary);
}

.card-header {
  background: var(--bg-card);
  border-bottom: var(--border-subtle);
  border-radius: 0;
  padding: 1.25rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
  background: var(--bg-card);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-subtle);
  padding: 1rem 1.5rem;
}

.card-title {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  letter-spacing: -0.01em;
}

.card-text {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* ===== MINIMAL BUTTONS ===== */
.btn {
  border-radius: var(--radius-sm);
  font-weight: 500;
  padding: 0.6rem 1.2rem;
  border: 1px solid transparent;
  transition: all 0.15s ease;
  font-size: 0.875rem;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.btn-primary {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.btn-primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
  color: white;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-light);
}

.btn-secondary:hover {
  background: var(--accent-secondary);
  color: white;
  border-color: var(--accent-secondary);
}

.btn-success {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn-success:hover {
  background: #047857;
  border-color: #047857;
  color: white;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background: #b45309;
  border-color: #b45309;
  color: white;
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background: #b91c1c;
  border-color: #b91c1c;
  color: white;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

/* Forms */
.form-control, .form-select {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  background: var(--bg-secondary);
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  color: var(--text-primary);
  outline: none;
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Badges */
.badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.5;
}

.bg-warning {
  background: var(--warning-color) !important;
  color: white !important;
}

.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
}

.bg-success {
  background: var(--success-color) !important;
  color: white !important;
}

.bg-info {
  background: var(--info-color) !important;
  color: white !important;
}

/* Task status badge styling */
.task-status-badge {
  transition: all 0.3s ease;
}

.task-status-badge.bg-warning {
  background: var(--warning-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.task-status-badge.bg-primary {
  background: var(--accent-primary) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.task-status-badge.bg-info {
  background: var(--info-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.task-status-badge.bg-success {
  background: var(--success-color) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

/* Alerts */
.alert {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  border-left: 3px solid var(--accent-primary);
  padding: 1rem;
}

.alert-info {
  border-left-color: var(--accent-primary);
  background: rgba(59, 130, 246, 0.1);
}

.alert-success {
  border-left-color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.alert-warning {
  border-left-color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
}

.alert-danger {
  border-left-color: var(--danger-color);
  background: rgba(239, 68, 68, 0.1);
}

/* Modal */
.modal-content {
  background: var(--bg-secondary);
  border: var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-medium);
}

.modal-header {
  background: var(--bg-secondary);
  border-bottom: var(--border-light);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.modal-title {
  color: var(--text-primary);
  font-weight: 600;
}

.modal-body {
  color: var(--text-secondary);
}

.modal-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-light);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Pagination */
.pagination {
  margin-top: 2rem;
}

.page-link {
  background: var(--bg-secondary);
  border: var(--border-light);
  color: var(--text-secondary);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.page-link:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.page-item.active .page-link {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

/* Special Effects */
.border-danger {
  border-color: #ef4444 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  /* Enhanced custom burger menu on mobile */
  .custom-navbar-toggler {
    padding: 0.75rem;
  }

  .custom-toggler-icon {
    width: 28px;
    height: 20px;
  }

  .custom-toggler-icon span {
    height: 3px;
  }
}

/* Utility Classes */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* ===== MINIMAL TABLES ===== */
.table {
  margin-bottom: 0;
  background: var(--bg-card);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.table th {
  background: var(--bg-tertiary);
  border-bottom: var(--border-subtle);
  font-weight: 600;
  color: var(--text-primary);
  padding: 1.2rem 1rem;
  font-size: 0.875rem;
  letter-spacing: -0.01em;
  border-top: none;
}

.table td {
  padding: 1.2rem 1rem;
  border-bottom: var(--border-subtle);
  vertical-align: middle;
  color: var(--text-secondary);
  border-top: none;
}

.table-hover tbody tr:hover {
  background: var(--bg-tertiary);
  transition: background-color 0.15s ease;
}

.table-responsive {
  border-radius: var(--radius-md);
  overflow: hidden;
  border: var(--border-subtle);
}

/* Admin Cards Styling */
.border-warning {
  border-color: #f59e0b !important;
}

.table tbody tr:has(.badge.bg-warning) {
  background: rgba(245, 158, 11, 0.1);
}

.table tbody tr:has(.badge.bg-warning):hover {
  background: rgba(245, 158, 11, 0.15);
}

/* Mobile Employee Cards */
@media (max-width: 767.98px) {
  .card .card {
    border: var(--border-light);
    box-shadow: var(--shadow-subtle);
  }

  .card .card:hover {
    transform: none;
    box-shadow: var(--shadow-medium);
  }

  .card .card.border-warning {
    background: rgba(245, 158, 11, 0.1);
  }
}

/* Custom Scrollbar for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}

/* Additional Dark Theme Enhancements */
.border-danger {
  border-color: var(--danger-color) !important;
  box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3);
}

/* Enhanced focus states for dark theme */
.btn:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Dark theme specific hover effects */
.navbar-brand:hover {
  color: var(--accent-primary) !important;
}

/* Better contrast for text elements */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* Bootstrap overrides for dark theme */
.dropdown-menu {
  background: var(--bg-secondary);
  border: var(--border-light);
  box-shadow: var(--shadow-medium);
}

.dropdown-item {
  color: var(--text-secondary);
}

.dropdown-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Dark theme for close buttons */
.btn-close {
  filter: invert(1);
}

/* Dark theme for modals backdrop */
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Enhanced table styling for dark theme */
.table {
  color: var(--text-secondary);
}

.table th {
  color: var(--text-primary);
  border-color: var(--border-light);
}

.table td {
  border-color: var(--border-light);
}

/* Dark theme for pagination */
.page-item.disabled .page-link {
  background: var(--bg-tertiary);
  border-color: var(--border-light);
  color: var(--text-muted);
}

/* Professional glow effects for interactive elements */
.card:hover,
.btn:hover {
  transition: all 0.3s ease;
}

/* Dark theme for form validation */
.form-control.is-invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

/* ===== NEW MINIMAL EMPLOYEES GRID ===== */

.employees-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.employee-card {
  background: var(--bg-card);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: var(--border-subtle);
  box-shadow: var(--shadow-card);
  transition: all 0.2s ease;
  position: relative;
}

/* ===== PHOTO SETTINGS GEAR ===== */
.photo-settings {
  position: absolute;
  top: 0.8rem;
  right: 0.8rem;
  z-index: 10;
}

.settings-btn {
  background: var(--bg-tertiary);
  border: var(--border-subtle);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  transition: all 0.2s ease;
  cursor: pointer;
}

.settings-btn:hover {
  background: var(--accent-primary);
  color: white;
  transform: rotate(90deg);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.settings-btn i {
  font-size: 0.8rem;
}

.employee-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: var(--accent-primary);
}

.admin-card {
  border-left: 3px solid var(--warning-color);
}

.admin-card:hover {
  border-left-color: var(--warning-color);
  box-shadow: 0 6px 20px rgba(217, 119, 6, 0.15);
}

/* ===== NEW MINIMAL EMPLOYEE AVATAR ===== */
.employee-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.avatar-circle.employee-avatar {
  background: var(--accent-primary);
}

.avatar-circle.admin-avatar {
  background: var(--warning-color);
}

.avatar-initials {
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
  letter-spacing: -0.01em;
}

/* ===== PROFILE PHOTO ===== */
.profile-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* ===== NEW EMPLOYEE INFO ===== */
.employee-info {
  text-align: center;
  margin-bottom: 1.5rem;
}

.employee-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.3rem;
}

.employee-phone {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-bottom: 0.8rem;
}

.employee-role {
  margin-bottom: 0;
}

.role-badge {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.role-badge.admin-role {
  background: rgba(217, 119, 6, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

.role-badge.employee-role {
  background: rgba(37, 99, 235, 0.1);
  color: var(--accent-primary);
  border: 1px solid rgba(37, 99, 235, 0.2);
}

/* ===== NEW EMPLOYEE STATS ===== */
.employee-stats {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 0.8rem;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  border: var(--border-subtle);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.2rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== ADMIN STATS ===== */
.admin-stats {
  margin-bottom: 1.5rem;
}

.admin-badge {
  text-align: center;
  padding: 0.8rem;
  background: rgba(217, 119, 6, 0.1);
  border: 1px solid rgba(217, 119, 6, 0.2);
  border-radius: var(--radius-sm);
  color: var(--warning-color);
  font-size: 0.85rem;
  font-weight: 500;
}

/* ===== EMPLOYEE ACTIONS ===== */
.employee-actions {
  text-align: center;
}

.action-btn {
  display: inline-block;
  padding: 0.6rem 1.2rem;
  background: var(--accent-primary);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #1d4ed8;
  color: white;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.admin-actions {
  text-align: center;
}

.admin-status {
  display: inline-block;
  padding: 0.6rem 1.2rem;
  background: var(--bg-tertiary);
  color: var(--text-muted);
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
}

/* ===== MOBILE RESPONSIVE ===== */
@media (max-width: 767.98px) {
  .employees-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .employee-card {
    padding: 1.2rem;
  }

  .employee-stats {
    gap: 0.8rem;
  }

  .stat-item {
    padding: 0.6rem;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

/* ===== PHOTO MODAL STYLES ===== */
.modal-content {
  background: var(--bg-card);
  border: var(--border-subtle);
  border-radius: var(--radius-md);
}

.modal-header {
  background: var(--bg-tertiary);
  border-bottom: var(--border-subtle);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.modal-title {
  color: var(--text-primary);
  font-weight: 600;
}

.btn-close {
  filter: invert(1);
}

.current-photo-preview {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.preview-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-primary);
  overflow: hidden;
  position: relative;
}

.current-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.preview-initials {
  font-size: 2rem;
  font-weight: 600;
  color: white;
  letter-spacing: -0.02em;
}

.form-label {
  color: var(--text-primary);
  font-weight: 500;
}

.form-control {
  background: var(--bg-tertiary);
  border: var(--border-subtle);
  color: var(--text-primary);
}

.form-control:focus {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-text {
  color: var(--text-muted);
}

/* ===== INPUT GROUP STYLES ===== */
.input-group-text {
  background: var(--bg-tertiary);
  border: var(--border-subtle);
  color: var(--text-primary);
  font-weight: 500;
}

.input-group .form-control {
  border-left: none;
}

.input-group .form-control:focus {
  border-left: none;
  box-shadow: none;
}

.input-group:focus-within .input-group-text {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* ===== ARCHIVE PAGE STYLES ===== */
.archive-header {
  margin-bottom: 2rem;
}

.archive-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
}

.archive-subtitle {
  color: var(--text-muted);
  font-size: 0.95rem;
  margin-bottom: 0;
}

.stats-badge {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  border: var(--border-subtle);
}

/* ===== ARCHIVE FILTERS ===== */
.archive-filters {
  background: var(--bg-card);
  border: var(--border-subtle);
  border-radius: var(--radius-md);
  padding: 1.5rem;
}

.filter-form .form-label {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.filter-form .form-control,
.filter-form .form-select {
  background: var(--bg-tertiary);
  border: var(--border-subtle);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* ===== ARCHIVE TASKS GRID ===== */
.archive-tasks-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.archive-task-card {
  background: var(--bg-card);
  border: var(--border-subtle);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  transition: all 0.15s ease;
  position: relative;
}

.archive-task-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-card-hover);
}

/* ===== TASK HEADER ===== */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.task-title-section {
  flex: 1;
}

.task-title {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.task-date {
  color: var(--text-muted);
  font-size: 0.85rem;
  font-weight: 500;
}

.task-type-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.02em;
}

.task-type-badge.personal {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.task-type-badge.general {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.task-status {
  flex-shrink: 0;
}

.status-badge {
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.4rem 0.8rem;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.02em;
}

.status-badge.completed {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

/* ===== TASK DESCRIPTION ===== */
.task-description {
  margin-bottom: 1.25rem;
}

.task-description p {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 0;
}

/* ===== TASK INFO GRID ===== */
.task-info {
  border-top: var(--border-subtle);
  padding-top: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  color: var(--text-muted);
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.02em;
}

.info-value {
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 500;
}

/* ===== EMPTY ARCHIVE ===== */
.empty-archive {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.empty-content {
  max-width: 400px;
}

.empty-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.empty-subtitle {
  color: var(--text-muted);
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.empty-actions {
  margin-top: 1.5rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .archive-filters {
    padding: 1rem;
  }

  .filter-form .row {
    gap: 1rem;
  }

  .filter-form .col-md-3 {
    width: 100%;
  }

  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .task-meta {
    gap: 0.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .archive-task-card {
    padding: 1rem;
  }

  .archive-task-card:hover {
    transform: none;
  }
}

/* Old admin and action styles removed */

/* Old responsive styles removed - using new minimal responsive design */

/* Remove excessive animations for minimal design */

/* ===== ARCHIVE TASKS STYLING ===== */

.archive-task-card {
  opacity: 0.9;
  transition: all 0.3s ease;
  border-left: 4px solid var(--success-color);
}

.archive-task-card:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.archive-task-card .card-header {
  background: linear-gradient(135deg, var(--bg-secondary), rgba(16, 185, 129, 0.1));
}

.archive-task-card .card-footer {
  border-top: 1px solid rgba(16, 185, 129, 0.3);
}

.archive-stats .badge {
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Archive page specific styling */
.archive-task-card .task-status-badge {
  background: linear-gradient(135deg, var(--success-color), #059669) !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.6);
  }
}