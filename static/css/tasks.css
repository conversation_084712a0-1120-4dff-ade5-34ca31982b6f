/* Task List */
.task-list {
  display: grid;
  gap: 1rem;
  padding: 1rem 0;
}

.task-card {
  background-color: var(--bg-secondary);
  border-radius: 0.5rem;
  padding: 1.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  transition: all 0.2s;
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow), 0 4px 12px rgba(0, 0, 0, 0.05);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.task-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.task-meta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.task-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.task-status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-waiting {
  background-color: var(--warning);
  color: #7c2d12;
}

.status-in-progress {
  background-color: var(--accent);
  color: white;
}

.status-completed {
  background-color: var(--success);
  color: white;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
}

.task-deadline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.task-deadline.overdue {
  color: var(--error);
}

/* Task Filters */
.task-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  gap: 0.5rem;
}

/* Task Form */
.task-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--bg-secondary);
  border-radius: 0.5rem;
  box-shadow: var(--shadow);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.page-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0 0.5rem;
  border-radius: 0.375rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border);
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.2s;
}

.page-item:hover {
  background-color: var(--hover);
}

.page-item.active {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
}

/* Animations */
.task-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Task Type Badge */
.task-type {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.type-personal {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border);
}

.type-general {
  background-color: var(--accent);
  color: white;
} 