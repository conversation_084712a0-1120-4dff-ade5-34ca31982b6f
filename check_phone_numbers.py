#!/usr/bin/env python3
"""
Проверяет номера телефонов в базе данных
"""

import sqlite3
import os

def check_phone_numbers():
    """Проверяет номера телефонов в базе данных"""
    
    db_path = 'instance/tasks.db'
    
    if not os.path.exists(db_path):
        print("❌ База данных не найдена")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Получаем всех пользователей
        cursor.execute("SELECT id, name, phone, is_admin FROM user")
        users = cursor.fetchall()
        
        print("📋 Пользователи в базе данных:")
        print("=" * 60)
        
        for user_id, name, phone, is_admin in users:
            role = "Администратор" if is_admin else "Сотрудник"
            print(f"ID: {user_id}")
            print(f"Имя: {name}")
            print(f"Телефон: {phone}")
            print(f"Роль: {role}")
            print("-" * 40)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Ошибка при проверке: {e}")

if __name__ == "__main__":
    check_phone_numbers()
