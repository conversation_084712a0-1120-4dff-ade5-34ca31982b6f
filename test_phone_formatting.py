#!/usr/bin/env python3
"""
Тестирование функций форматирования номеров телефонов
"""

def normalize_phone_number(phone):
    """Нормализует номер телефона для хранения в базе данных"""
    if not phone:
        return phone
    
    # Убираем все нецифровые символы
    digits = ''.join(filter(str.isdigit, phone))
    
    # Если номер 9 цифр, добавляем 998 в начало
    if len(digits) == 9:
        return f"998{digits}"
    # Если номер уже содержит 998 в начале
    elif len(digits) == 12 and digits.startswith('998'):
        return digits
    else:
        # Возвращаем как есть
        return phone

def format_phone_number(phone):
    """Форматирует номер телефона в красивый вид +998 (99) 895 32-00"""
    if not phone:
        return phone
    
    # Убираем все нецифровые символы
    digits = ''.join(filter(str.isdigit, phone))
    
    # Если номер 12 цифр и начинается с 998
    if len(digits) == 12 and digits.startswith('998'):
        # Формат: 998998953200 -> +998 (99) 895 32-00
        return f"+998 ({digits[3:5]}) {digits[5:8]} {digits[8:10]}-{digits[10:12]}"
    else:
        # Возвращаем как есть, если формат не распознан
        return phone

def test_phone_functions():
    """Тестирует функции обработки номеров телефонов"""
    
    print("🧪 Тестирование функций обработки номеров телефонов\n")
    
    # Тестовые случаи
    test_cases = [
        "998953200",      # 9 цифр
        "99 895 32 00",   # 9 цифр с пробелами
        "998998953200",   # 12 цифр
        "+998 99 895 32 00",  # уже отформатированный
        "998-99-895-32-00",   # с дефисами
        "123456789",      # неправильный формат
    ]
    
    print("📋 Результаты тестирования:")
    print("=" * 60)
    
    for i, test_phone in enumerate(test_cases, 1):
        normalized = normalize_phone_number(test_phone)
        formatted = format_phone_number(normalized)
        
        print(f"{i}. Исходный:     '{test_phone}'")
        print(f"   Нормализован: '{normalized}'")
        print(f"   Отформатирован: '{formatted}'")
        print("-" * 40)
    
    print("\n✅ Тестирование завершено!")

if __name__ == "__main__":
    test_phone_functions()
